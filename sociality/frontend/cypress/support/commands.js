// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom command for login (adjust based on your authentication system)
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password123') => {
  cy.visit('/login');
  cy.get('[data-testid="email-input"]').type(email);
  cy.get('[data-testid="password-input"]').type(password);
  cy.get('[data-testid="login-button"]').click();
  cy.url().should('not.include', '/login');
});

// Custom command for logout
Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="logout-button"]').click();
  cy.url().should('include', '/login');
});

// Custom command to wait for API calls
Cypress.Commands.add('waitForApi', (alias) => {
  cy.wait(alias).then((interception) => {
    expect(interception.response.statusCode).to.be.oneOf([200, 201, 204]);
  });
});

// Custom command to create a test post
Cypress.Commands.add('createPost', (content = 'Test post content') => {
  cy.get('[data-testid="create-post-input"]').type(content);
  cy.get('[data-testid="create-post-button"]').click();
  cy.contains(content).should('be.visible');
});

// Custom command to clear local storage and cookies
Cypress.Commands.add('clearAppData', () => {
  cy.clearLocalStorage();
  cy.clearCookies();
  cy.window().then((win) => {
    win.sessionStorage.clear();
  });
});

// Custom command to mock API responses
Cypress.Commands.add('mockApiResponse', (method, url, response, statusCode = 200) => {
  cy.intercept(method, url, {
    statusCode,
    body: response,
  });
});

// Custom command to check responsive design
Cypress.Commands.add('checkResponsive', () => {
  // Test mobile view
  cy.viewport(375, 667);
  cy.wait(500);
  
  // Test tablet view
  cy.viewport(768, 1024);
  cy.wait(500);
  
  // Test desktop view
  cy.viewport(1280, 720);
  cy.wait(500);
});
