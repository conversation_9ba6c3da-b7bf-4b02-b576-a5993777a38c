describe('Navigation', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock API calls
    cy.intercept('GET', '/api/posts/feed', { fixture: 'posts.json' }).as('getFeed');
    cy.intercept('GET', '/api/users/suggested', { fixture: 'users.json' }).as('getSuggestedUsers');
  });

  it('should navigate to home page when authenticated', () => {
    cy.visit('/');
    cy.wait('@getFeed');
    cy.url().should('eq', Cypress.config().baseUrl + '/');
    cy.get('[alt="logo"]').should('be.visible');
  });

  it('should redirect to auth page when not authenticated', () => {
    cy.clearAppData();
    cy.visit('/');
    cy.url().should('include', '/auth');
  });

  it('should navigate between main sections', () => {
    cy.visit('/');
    cy.wait('@getFeed');

    // Test navigation to search
    cy.get('button').contains('svg').first().click(); // Assuming search icon is first
    cy.url().should('include', '/search');

    // Test navigation to notifications
    cy.visit('/notifications');
    cy.url().should('include', '/notifications');

    // Test navigation to chat
    cy.visit('/chat');
    cy.url().should('include', '/chat');

    // Test navigation to settings
    cy.visit('/settings');
    cy.url().should('include', '/settings');

    // Test navigation to profile
    cy.visit('/testuser');
    cy.url().should('include', '/testuser');
  });

  it('should show logo and navigate to home when clicked', () => {
    cy.visit('/settings');
    cy.get('[alt="logo"]').click();
    cy.url().should('eq', Cypress.config().baseUrl + '/');
  });

  it('should handle responsive navigation', () => {
    cy.visit('/');
    cy.wait('@getFeed');

    // Test mobile view
    cy.viewport(375, 667);
    cy.get('[alt="logo"]').should('be.visible');

    // Test tablet view
    cy.viewport(768, 1024);
    cy.get('[alt="logo"]').should('be.visible');

    // Test desktop view
    cy.viewport(1280, 720);
    cy.get('[alt="logo"]').should('be.visible');
  });

  it('should navigate to user profile from username link', () => {
    cy.visit('/');
    cy.wait('@getFeed');

    // Mock user profile API
    cy.intercept('GET', '/api/users/profile/testuser', {
      statusCode: 200,
      body: {
        _id: '1',
        name: 'Test User',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        followers: [],
        following: []
      }
    }).as('getUserProfile');

    cy.visit('/testuser');
    cy.wait('@getUserProfile');
    cy.url().should('include', '/testuser');
  });

  it('should navigate to post detail page', () => {
    cy.visit('/');
    cy.wait('@getFeed');

    // Mock post detail API
    cy.intercept('GET', '/api/posts/testuser/123', {
      statusCode: 200,
      body: {
        _id: '123',
        text: 'Test post content',
        postedBy: {
          _id: '1',
          username: 'testuser',
          name: 'Test User'
        },
        createdAt: new Date().toISOString(),
        likes: [],
        replies: []
      }
    }).as('getPost');

    cy.visit('/testuser/post/123');
    cy.wait('@getPost');
    cy.url().should('include', '/testuser/post/123');
  });

  it('should handle 404 for non-existent routes', () => {
    cy.visit('/nonexistent-route');
    // Should redirect to home or show 404 page
    // Adjust based on your app's 404 handling
  });

  it('should maintain navigation state across page refreshes', () => {
    cy.visit('/settings');
    cy.reload();
    cy.url().should('include', '/settings');
  });
});
