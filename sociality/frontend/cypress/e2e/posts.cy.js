describe('Posts Functionality', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock API calls
    cy.intercept('GET', '/api/posts/feed', { fixture: 'posts.json' }).as('getFeed');
    cy.intercept('GET', '/api/users/suggested', { fixture: 'users.json' }).as('getSuggestedUsers');
  });

  describe('Viewing Posts', () => {
    it('should display posts feed on home page', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check if posts are displayed
      cy.contains('Just finished building an amazing React app!').should('be.visible');
      cy.contains('Beautiful sunset today!').should('be.visible');
    });

    it('should display post author information', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check author names and usernames
      cy.contains('<PERSON> Doe').should('be.visible');
      cy.contains('@john_doe').should('be.visible');
      cy.contains('Jane Smith').should('be.visible');
      cy.contains('@jane_smith').should('be.visible');
    });

    it('should display post engagement metrics', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check for like and comment counts
      cy.contains('25').should('be.visible'); // likes
      cy.contains('5').should('be.visible');  // comments
      cy.contains('42').should('be.visible'); // likes
      cy.contains('8').should('be.visible');  // comments
    });

    it('should handle empty feed gracefully', () => {
      cy.intercept('GET', '/api/posts/feed', { body: [] }).as('getEmptyFeed');
      cy.visit('/');
      cy.wait('@getEmptyFeed');

      // Should show empty state or no posts message
      cy.contains('No posts yet').should('be.visible');
    });
  });

  describe('Creating Posts', () => {
    beforeEach(() => {
      cy.visit('/');
      cy.wait('@getFeed');
    });

    it('should display create post form', () => {
      // Look for create post input/textarea
      cy.get('textarea, input').should('contain.value', '').should('be.visible');
      cy.contains('Post', { matchCase: false }).should('be.visible');
    });

    it('should create a new post', () => {
      const postContent = 'This is a test post from Cypress!';

      // Mock create post API
      cy.intercept('POST', '/api/posts/create', {
        statusCode: 200,
        body: {
          _id: 'new-post-id',
          text: postContent,
          postedBy: {
            _id: '1',
            username: 'testuser',
            name: 'Test User'
          },
          createdAt: new Date().toISOString(),
          likes: [],
          replies: []
        }
      }).as('createPost');

      // Find and fill the create post input
      cy.get('textarea, input[placeholder*="post"], input[placeholder*="share"]')
        .first()
        .type(postContent);

      // Click post button
      cy.contains('button', 'Post').click();

      cy.wait('@createPost');
      cy.contains(postContent).should('be.visible');
    });

    it('should handle post creation errors', () => {
      cy.intercept('POST', '/api/posts/create', {
        statusCode: 400,
        body: { error: 'Post content is required' }
      }).as('createPostError');

      // Try to create empty post
      cy.contains('button', 'Post').click();

      cy.wait('@createPostError');
      cy.contains('Post content is required').should('be.visible');
    });

    it('should enforce character limit', () => {
      const longContent = 'a'.repeat(501); // Assuming 500 char limit

      cy.get('textarea, input[placeholder*="post"], input[placeholder*="share"]')
        .first()
        .type(longContent);

      // Should show character count warning
      cy.contains('500').should('be.visible');
      cy.contains('button', 'Post').should('be.disabled');
    });
  });

  describe('Post Interactions', () => {
    beforeEach(() => {
      cy.visit('/');
      cy.wait('@getFeed');
    });

    it('should like a post', () => {
      // Mock like post API
      cy.intercept('PUT', '/api/posts/like/*', {
        statusCode: 200,
        body: { message: 'Post liked successfully' }
      }).as('likePost');

      // Click like button (heart icon)
      cy.get('button').contains('svg').first().click();

      cy.wait('@likePost');
      // Should show updated like count or visual feedback
    });

    it('should unlike a post', () => {
      // Mock unlike post API
      cy.intercept('PUT', '/api/posts/like/*', {
        statusCode: 200,
        body: { message: 'Post unliked successfully' }
      }).as('unlikePost');

      // Click like button twice (like then unlike)
      cy.get('button').contains('svg').first().click();
      cy.get('button').contains('svg').first().click();

      cy.wait('@unlikePost');
    });

    it('should navigate to post detail when clicked', () => {
      // Mock post detail API
      cy.intercept('GET', '/api/posts/john_doe/*', {
        statusCode: 200,
        body: {
          _id: '1',
          text: 'Just finished building an amazing React app! 🚀',
          postedBy: {
            _id: '1',
            username: 'john_doe',
            name: 'John Doe'
          },
          createdAt: '2024-01-15T10:30:00Z',
          likes: [],
          replies: []
        }
      }).as('getPostDetail');

      // Click on post content or timestamp
      cy.contains('Just finished building an amazing React app!').click();

      cy.wait('@getPostDetail');
      cy.url().should('include', '/john_doe/post/');
    });

    it('should open comment section', () => {
      // Click comment button
      cy.get('button').contains('svg').eq(1).click(); // Assuming comment icon is second

      // Should show comment input or navigate to post detail
      cy.get('textarea, input[placeholder*="comment"], input[placeholder*="reply"]')
        .should('be.visible');
    });
  });

  describe('Post Detail Page', () => {
    beforeEach(() => {
      // Mock post detail API
      cy.intercept('GET', '/api/posts/testuser/123', {
        statusCode: 200,
        body: {
          _id: '123',
          text: 'This is a detailed post view',
          postedBy: {
            _id: '1',
            username: 'testuser',
            name: 'Test User'
          },
          createdAt: new Date().toISOString(),
          likes: [],
          replies: [
            {
              _id: 'reply1',
              text: 'Great post!',
              userProfilePic: '',
              username: 'commenter1',
              name: 'Commenter One'
            }
          ]
        }
      }).as('getPostDetail');

      cy.visit('/testuser/post/123');
      cy.wait('@getPostDetail');
    });

    it('should display post content and details', () => {
      cy.contains('This is a detailed post view').should('be.visible');
      cy.contains('Test User').should('be.visible');
      cy.contains('@testuser').should('be.visible');
    });

    it('should display comments/replies', () => {
      cy.contains('Great post!').should('be.visible');
      cy.contains('Commenter One').should('be.visible');
    });

    it('should allow adding comments', () => {
      const commentText = 'This is a test comment';

      // Mock add comment API
      cy.intercept('PUT', '/api/posts/reply/123', {
        statusCode: 200,
        body: {
          _id: 'new-reply',
          text: commentText,
          userProfilePic: '',
          username: 'testuser',
          name: 'Test User'
        }
      }).as('addComment');

      cy.get('textarea, input[placeholder*="comment"], input[placeholder*="reply"]')
        .type(commentText);

      cy.contains('button', 'Reply').click();

      cy.wait('@addComment');
      cy.contains(commentText).should('be.visible');
    });
  });
});
