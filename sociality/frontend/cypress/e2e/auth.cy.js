describe('Authentication Flow', () => {
  beforeEach(() => {
    // Clear any existing user data
    cy.clearAppData();
    cy.visit('/auth');
  });

  describe('Login', () => {
    it('should display login form by default', () => {
      cy.contains('Welcome back').should('be.visible');
      cy.get('input[placeholder="Enter your username"]').should('be.visible');
      cy.get('input[placeholder="Enter your password"]').should('be.visible');
      cy.contains('button', 'Login').should('be.visible');
    });

    it('should switch to signup form', () => {
      cy.contains('Sign up').click();
      cy.contains('Create your account').should('be.visible');
      cy.get('input[placeholder="Enter your full name"]').should('be.visible');
      cy.get('input[placeholder="Choose a username"]').should('be.visible');
      cy.get('input[placeholder="Enter your email"]').should('be.visible');
      cy.get('input[placeholder="Enter your password"]').should('be.visible');
    });

    it('should show validation errors for empty fields', () => {
      cy.contains('button', 'Login').click();
      // The form should not submit and stay on the same page
      cy.url().should('include', '/auth');
    });

    it('should show error for invalid credentials', () => {
      cy.get('input[placeholder="Enter your username"]').type('invaliduser');
      cy.get('input[placeholder="Enter your password"]').type('wrongpassword');
      cy.contains('button', 'Login').click();
      
      // Should show error toast (adjust selector based on your toast implementation)
      cy.contains('Invalid username or password').should('be.visible');
    });

    it('should toggle password visibility', () => {
      const passwordInput = cy.get('input[placeholder="Enter your password"]');
      
      // Initially password should be hidden
      passwordInput.should('have.attr', 'type', 'password');
      
      // Click the eye icon to show password
      cy.get('button').contains('svg').click();
      passwordInput.should('have.attr', 'type', 'text');
      
      // Click again to hide password
      cy.get('button').contains('svg').click();
      passwordInput.should('have.attr', 'type', 'password');
    });

    it('should login with valid credentials', () => {
      // Mock the login API call
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: ''
        }
      }).as('loginRequest');

      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();

      cy.wait('@loginRequest');
      cy.url().should('not.include', '/auth');
      cy.url().should('eq', Cypress.config().baseUrl + '/');
    });
  });

  describe('Signup', () => {
    beforeEach(() => {
      cy.contains('Sign up').click();
    });

    it('should display signup form', () => {
      cy.contains('Create your account').should('be.visible');
      cy.get('input[placeholder="Enter your full name"]').should('be.visible');
      cy.get('input[placeholder="Choose a username"]').should('be.visible');
      cy.get('input[placeholder="Enter your email"]').should('be.visible');
      cy.get('input[placeholder="Enter your password"]').should('be.visible');
      cy.contains('button', 'Sign up').should('be.visible');
    });

    it('should switch back to login form', () => {
      cy.contains('Login').click();
      cy.contains('Welcome back').should('be.visible');
    });

    it('should signup with valid data', () => {
      // Mock the signup API call
      cy.intercept('POST', '/api/users/signup', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'New User',
          email: '<EMAIL>',
          username: 'newuser',
          bio: '',
          profilePic: ''
        }
      }).as('signupRequest');

      cy.get('input[placeholder="Enter your full name"]').type('New User');
      cy.get('input[placeholder="Choose a username"]').type('newuser');
      cy.get('input[placeholder="Enter your email"]').type('<EMAIL>');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Sign up').click();

      cy.wait('@signupRequest');
      cy.url().should('not.include', '/auth');
    });

    it('should show error for duplicate username', () => {
      cy.intercept('POST', '/api/users/signup', {
        statusCode: 400,
        body: { error: 'Username already exists' }
      }).as('signupError');

      cy.get('input[placeholder="Enter your full name"]').type('Test User');
      cy.get('input[placeholder="Choose a username"]').type('existinguser');
      cy.get('input[placeholder="Enter your email"]').type('<EMAIL>');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Sign up').click();

      cy.wait('@signupError');
      cy.contains('Username already exists').should('be.visible');
    });
  });

  describe('Google OAuth', () => {
    it('should display Google login button', () => {
      cy.contains('Continue with Google').should('be.visible');
    });

    // Note: Testing actual OAuth flow requires special setup
    // This is a placeholder for OAuth testing
    it('should handle Google OAuth flow', () => {
      // Mock OAuth success
      cy.window().then((win) => {
        win.localStorage.setItem('user-threads', JSON.stringify({
          _id: '1',
          name: 'Google User',
          email: '<EMAIL>',
          username: 'googleuser',
          isGoogleUser: true
        }));
      });

      cy.reload();
      cy.url().should('not.include', '/auth');
    });
  });
});
