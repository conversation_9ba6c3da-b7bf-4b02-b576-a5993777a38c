describe('User Profile', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });
  });

  describe('Viewing Own Profile', () => {
    beforeEach(() => {
      // Mock own profile API
      cy.intercept('GET', '/api/users/profile/testuser', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: '',
          followers: ['2', '3'],
          following: ['4', '5'],
          posts: []
        }
      }).as('getOwnProfile');

      cy.visit('/testuser');
      cy.wait('@getOwnProfile');
    });

    it('should display profile information', () => {
      cy.contains('Test User').should('be.visible');
      cy.contains('@testuser').should('be.visible');
      cy.contains('Test bio').should('be.visible');
    });

    it('should display follower and following counts', () => {
      cy.contains('2').should('be.visible'); // followers count
      cy.contains('2').should('be.visible'); // following count
    });

    it('should show edit profile button for own profile', () => {
      cy.contains('Edit Profile').should('be.visible');
    });

    it('should navigate to edit profile page', () => {
      cy.contains('Edit Profile').click();
      cy.url().should('include', '/update');
    });

    it('should display user posts', () => {
      // Mock user posts
      cy.intercept('GET', '/api/users/posts/testuser', {
        statusCode: 200,
        body: [
          {
            _id: 'post1',
            text: 'My first post',
            postedBy: {
              _id: '1',
              username: 'testuser',
              name: 'Test User'
            },
            createdAt: new Date().toISOString(),
            likes: [],
            replies: []
          }
        ]
      }).as('getUserPosts');

      cy.reload();
      cy.wait('@getUserPosts');
      cy.contains('My first post').should('be.visible');
    });
  });

  describe('Viewing Other User Profile', () => {
    beforeEach(() => {
      // Mock other user profile API
      cy.intercept('GET', '/api/users/profile/john_doe', {
        statusCode: 200,
        body: {
          _id: '2',
          name: 'John Doe',
          username: 'john_doe',
          bio: 'Software developer and tech enthusiast',
          profilePic: 'https://via.placeholder.com/150',
          followers: ['1'],
          following: ['3', '4'],
          posts: []
        }
      }).as('getOtherProfile');

      cy.visit('/john_doe');
      cy.wait('@getOtherProfile');
    });

    it('should display other user profile information', () => {
      cy.contains('John Doe').should('be.visible');
      cy.contains('@john_doe').should('be.visible');
      cy.contains('Software developer and tech enthusiast').should('be.visible');
    });

    it('should show follow button for other users', () => {
      cy.contains('Follow').should('be.visible');
    });

    it('should follow user', () => {
      // Mock follow API
      cy.intercept('POST', '/api/users/follow/2', {
        statusCode: 200,
        body: { message: 'User followed successfully' }
      }).as('followUser');

      cy.contains('Follow').click();
      cy.wait('@followUser');
      cy.contains('Unfollow').should('be.visible');
    });

    it('should unfollow user', () => {
      // Mock unfollow API
      cy.intercept('POST', '/api/users/follow/2', {
        statusCode: 200,
        body: { message: 'User unfollowed successfully' }
      }).as('unfollowUser');

      // First follow, then unfollow
      cy.contains('Follow').click();
      cy.contains('Unfollow').click();
      cy.wait('@unfollowUser');
      cy.contains('Follow').should('be.visible');
    });

    it('should show message button for other users', () => {
      cy.get('button').should('contain.text', 'Message').or('contain', 'Chat');
    });

    it('should navigate to chat when message button clicked', () => {
      cy.get('button').contains('Message').click();
      cy.url().should('include', '/chat');
    });
  });

  describe('Profile Tabs', () => {
    beforeEach(() => {
      cy.intercept('GET', '/api/users/profile/testuser', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: '',
          followers: [],
          following: [],
          posts: []
        }
      }).as('getProfile');

      cy.visit('/testuser');
      cy.wait('@getProfile');
    });

    it('should switch between posts and replies tabs', () => {
      // Check if tabs exist
      cy.contains('Posts').should('be.visible');
      cy.contains('Replies').should('be.visible');

      // Click on replies tab
      cy.contains('Replies').click();
      // Should show replies content or empty state

      // Click back to posts tab
      cy.contains('Posts').click();
      // Should show posts content
    });
  });

  describe('Followers/Following Modal', () => {
    beforeEach(() => {
      cy.intercept('GET', '/api/users/profile/testuser', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: '',
          followers: ['2', '3'],
          following: ['4', '5'],
          posts: []
        }
      }).as('getProfile');

      cy.visit('/testuser');
      cy.wait('@getProfile');
    });

    it('should open followers modal', () => {
      // Mock followers API
      cy.intercept('GET', '/api/users/1/followers', {
        statusCode: 200,
        body: [
          { _id: '2', username: 'follower1', name: 'Follower One' },
          { _id: '3', username: 'follower2', name: 'Follower Two' }
        ]
      }).as('getFollowers');

      cy.contains('2 followers').click();
      cy.wait('@getFollowers');
      
      cy.contains('Followers').should('be.visible');
      cy.contains('Follower One').should('be.visible');
      cy.contains('Follower Two').should('be.visible');
    });

    it('should open following modal', () => {
      // Mock following API
      cy.intercept('GET', '/api/users/1/following', {
        statusCode: 200,
        body: [
          { _id: '4', username: 'following1', name: 'Following One' },
          { _id: '5', username: 'following2', name: 'Following Two' }
        ]
      }).as('getFollowing');

      cy.contains('2 following').click();
      cy.wait('@getFollowing');
      
      cy.contains('Following').should('be.visible');
      cy.contains('Following One').should('be.visible');
      cy.contains('Following Two').should('be.visible');
    });

    it('should close modal when clicking outside or close button', () => {
      cy.contains('2 followers').click();
      
      // Close modal (adjust selector based on your modal implementation)
      cy.get('[data-testid="modal-close"], .modal-close, button').contains('×').click();
      cy.contains('Followers').should('not.exist');
    });
  });

  describe('Profile Not Found', () => {
    it('should handle non-existent user profile', () => {
      cy.intercept('GET', '/api/users/profile/nonexistentuser', {
        statusCode: 404,
        body: { error: 'User not found' }
      }).as('getProfileNotFound');

      cy.visit('/nonexistentuser');
      cy.wait('@getProfileNotFound');
      
      cy.contains('User not found').should('be.visible');
    });
  });

  describe('Responsive Profile View', () => {
    beforeEach(() => {
      cy.intercept('GET', '/api/users/profile/testuser', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: '',
          followers: [],
          following: [],
          posts: []
        }
      }).as('getProfile');

      cy.visit('/testuser');
      cy.wait('@getProfile');
    });

    it('should display correctly on mobile', () => {
      cy.viewport(375, 667);
      cy.contains('Test User').should('be.visible');
      cy.contains('Edit Profile').should('be.visible');
    });

    it('should display correctly on tablet', () => {
      cy.viewport(768, 1024);
      cy.contains('Test User').should('be.visible');
      cy.contains('Edit Profile').should('be.visible');
    });
  });
});
