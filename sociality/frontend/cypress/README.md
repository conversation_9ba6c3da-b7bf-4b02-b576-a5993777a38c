# Cypress Testing Setup

This directory contains the Cypress testing setup for the Sociality frontend application.

## Overview

Cypress is configured for both end-to-end (E2E) testing and component testing:

- **E2E Tests**: Test complete user workflows across the entire application
- **Component Tests**: Test individual React components in isolation

## Directory Structure

```
cypress/
├── e2e/                    # End-to-end tests
│   ├── auth.cy.js         # Authentication flow tests
│   ├── navigation.cy.js   # Navigation and routing tests
│   ├── posts.cy.js        # Posts functionality tests
│   └── user-profile.cy.js # User profile tests
├── component/             # Component tests
│   ├── LoginCard.cy.jsx   # LoginCard component tests
│   └── Header.cy.jsx      # Header component tests
├── fixtures/              # Test data
│   ├── users.json         # Sample user data
│   └── posts.json         # Sample post data
├── support/               # Support files
│   ├── commands.js        # Custom Cypress commands
│   ├── component.js       # Component testing setup
│   └── e2e.js            # E2E testing setup
└── README.md             # This file
```

## Getting Started

### Prerequisites

1. Make sure your development server is running:
   ```bash
   npm run dev
   ```

2. Make sure your backend server is running (if testing with real API):
   ```bash
   cd ../.. && npm run dev
   ```

### Running Tests

#### E2E Tests

**Interactive Mode (Recommended for development):**
```bash
npm run test:e2e:open
# or
npm run cypress:open
```

**Headless Mode (For CI/CD):**
```bash
npm run test:e2e
# or
npm run cypress:run
```

#### Component Tests

**Interactive Mode:**
```bash
npm run test:component:open
```

**Headless Mode:**
```bash
npm run test:component
```

## Test Configuration

The Cypress configuration is defined in `cypress.config.js`:

- **Base URL**: `http://localhost:5173` (Vite dev server)
- **Viewport**: 1280x720 (default)
- **Timeouts**: 10 seconds for commands and requests
- **Video Recording**: Enabled for test runs
- **Screenshots**: Enabled on test failures

## Custom Commands

We've created several custom Cypress commands to make testing easier:

### Authentication Commands
- `cy.login(email, password)` - Log in a user
- `cy.logout()` - Log out the current user
- `cy.clearAppData()` - Clear localStorage, cookies, and sessionStorage

### API Commands
- `cy.waitForApi(alias)` - Wait for API calls and verify success
- `cy.mockApiResponse(method, url, response, statusCode)` - Mock API responses

### Social Media Commands
- `cy.createPost(content)` - Create a new post
- `cy.checkResponsive()` - Test responsive design across viewports

## Writing Tests

### E2E Test Example

```javascript
describe('User Login', () => {
  beforeEach(() => {
    cy.clearAppData();
    cy.visit('/auth');
  });

  it('should login with valid credentials', () => {
    cy.get('input[placeholder="Enter your username"]').type('testuser');
    cy.get('input[placeholder="Enter your password"]').type('password123');
    cy.contains('button', 'Login').click();
    
    cy.url().should('not.include', '/auth');
    cy.contains('Welcome back').should('be.visible');
  });
});
```

### Component Test Example

```javascript
import LoginCard from '../../src/components/LoginCard';

describe('LoginCard Component', () => {
  it('should render login form', () => {
    cy.mount(<LoginCard />);
    
    cy.contains('Welcome back').should('be.visible');
    cy.get('input[placeholder="Enter your username"]').should('be.visible');
  });
});
```

## Best Practices

### 1. Use Data Attributes
Add `data-testid` attributes to elements for reliable selection:
```jsx
<button data-testid="login-button">Login</button>
```

### 2. Mock API Calls
Use `cy.intercept()` to mock API responses:
```javascript
cy.intercept('POST', '/api/users/login', { fixture: 'user.json' }).as('login');
```

### 3. Clean State
Always start tests with a clean state:
```javascript
beforeEach(() => {
  cy.clearAppData();
});
```

### 4. Wait for API Calls
Use aliases to wait for API calls:
```javascript
cy.wait('@loginRequest');
```

### 5. Test User Flows
Focus on testing complete user workflows rather than individual functions.

## Debugging Tests

### Interactive Mode
Run tests in interactive mode to see them execute in real-time:
```bash
npm run cypress:open
```

### Screenshots and Videos
Failed tests automatically capture screenshots and videos in:
- `cypress/screenshots/`
- `cypress/videos/`

### Browser DevTools
Use browser DevTools during interactive mode to inspect elements and debug issues.

## CI/CD Integration

For continuous integration, use headless mode:

```yaml
# GitHub Actions example
- name: Run Cypress tests
  run: |
    npm run dev &
    npm run test:e2e
```

## Troubleshooting

### Common Issues

1. **Tests timing out**: Increase timeout values in `cypress.config.js`
2. **Element not found**: Use `cy.get()` with proper selectors or data attributes
3. **API calls failing**: Ensure backend is running or mock the API calls
4. **Flaky tests**: Add proper waits and assertions

### Getting Help

- [Cypress Documentation](https://docs.cypress.io/)
- [Cypress Best Practices](https://docs.cypress.io/guides/references/best-practices)
- [Cypress Examples](https://github.com/cypress-io/cypress-example-recipes)

## Contributing

When adding new tests:

1. Follow the existing naming conventions
2. Add appropriate fixtures for test data
3. Use custom commands when possible
4. Test both happy path and error scenarios
5. Ensure tests are reliable and not flaky
