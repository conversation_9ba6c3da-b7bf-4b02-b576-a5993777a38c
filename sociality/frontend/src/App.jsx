import { Box, Container } from "@chakra-ui/react";
import { useLocation } from "react-router-dom";
import { Header } from "./components/layout";
import AppRoutes from "./routes/AppRoutes";

import useOAuthCallback from "./hooks/useOAuthCallback";
import useInitializeUser from "./hooks/useInitializeUser";


/**
 * Main application component
 * Provides the layout structure and routing for the application
 */
function App() {
	const { pathname } = useLocation();
	// Initialize user state from tab-specific storage
	useInitializeUser();

	// Handle OAuth callback on any page
	useOAuthCallback();

	return (
		<Box position={"relative"} w='full'>
			<Container
				maxW={pathname === "/" ? {
					base: "100%",    // Full width on mobile (320px+)
					xs: "100%",      // Full width on small mobile (320px+)
					sm: "100%",      // Full width on standard mobile (375px+)
					md: "100%",      // Full width on large mobile (414px+)
					lg: "480px",     // Constrained on small tablets (480px+)
					xl: "620px",     // Standard width on tablets (768px+)
					"2xl": "900px"   // Wider on desktops (1024px+)
				} : {
					base: "100%",    // Full width on mobile for all other pages
					xs: "100%",      // Full width on small mobile
					sm: "100%",      // Full width on standard mobile
					md: "100%",      // Full width on large mobile
					lg: "480px",     // Constrained on small tablets
					xl: "620px"      // Standard width on tablets and up
				}}
				px={{ base: 3, sm: 4, md: 6 }} // Responsive padding
			>
				{pathname !== "/auth" && pathname !== "/profile-setup" && <Header />}
				<AppRoutes />
			</Container>
		</Box>
	);
}

export default App;
