{"name": "passport-google-oauth20", "version": "2.0.0", "description": "Google (OAuth 2.0) authentication strategy for Passport.", "keywords": ["passport", "google", "auth", "authn", "authentication", "identity"], "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "repository": {"type": "git", "url": "git://github.com/jared<PERSON>son/passport-google-oauth2.git"}, "bugs": {"url": "http://github.com/jared<PERSON><PERSON>/passport-google-oauth2/issues"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "main": "./lib", "dependencies": {"passport-oauth2": "1.x.x"}, "devDependencies": {"make-node": "0.3.x", "mocha": "1.x.x", "chai": "2.x.x", "chai-passport-strategy": "1.x.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "node_modules/.bin/mocha --require test/bootstrap/node test/*.test.js test/**/*.test.js"}}