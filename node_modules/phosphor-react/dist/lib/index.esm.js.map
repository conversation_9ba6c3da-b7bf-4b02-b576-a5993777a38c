{"version": 3, "file": "index.esm.js", "sources": ["../../src/lib/index.ts"], "sourcesContent": ["import { createContext, ComponentPropsWithoutRef } from \"react\";\n\nexport type IconWeight =\n  | \"thin\"\n  | \"light\"\n  | \"regular\"\n  | \"bold\"\n  | \"fill\"\n  | \"duotone\";\n\nexport type PaintFunction = (color: string) => React.ReactNode | null;\n\nexport interface IconProps extends ComponentPropsWithoutRef<\"svg\"> {\n  alt?: string;\n  color?: string;\n  size?: string | number;\n  weight?: IconWeight;\n  mirrored?: boolean;\n}\n\ntype IconComponentProps = IconProps & React.RefAttributes<SVGSVGElement>;\n\nexport type Icon = React.ForwardRefExoticComponent<IconComponentProps>;\n\nexport const IconContext = createContext<IconProps>({\n  color: \"currentColor\",\n  size: \"1em\",\n  weight: \"regular\",\n  mirrored: false,\n});\n\nexport const renderPathForWeight = (\n  weight: IconWeight,\n  color: string,\n  pathsByWeight: Map<IconWeight, PaintFunction>\n): React.ReactNode | null => {\n  const path = pathsByWeight.get(weight);\n  if (!!path) return path(color);\n\n  console.error(\n    'Unsupported icon weight. Choose from \"thin\", \"light\", \"regular\", \"bold\", \"fill\", or \"duotone\".'\n  );\n\n  return null;\n};\n"], "names": ["IconContext", "createContext", "color", "size", "weight", "mirrored", "renderPathForWeight", "pathsByWeight", "path", "get", "console", "error"], "mappings": ";;IAwBaA,WAAW,gBAAGC,aAAa,CAAY;AAClDC,EAAAA,KAAK,EAAE,cAD2C;AAElDC,EAAAA,IAAI,EAAE,KAF4C;AAGlDC,EAAAA,MAAM,EAAE,SAH0C;AAIlDC,EAAAA,QAAQ,EAAE;AAJwC,CAAZ;IAO3BC,mBAAmB,GAAG,SAAtBA,mBAAsB,CACjCF,MADiC,EAEjCF,KAFiC,EAGjCK,aAHiC;AAKjC,MAAMC,IAAI,GAAGD,aAAa,CAACE,GAAd,CAAkBL,MAAlB,CAAb;AACA,MAAI,CAAC,CAACI,IAAN,EAAY,OAAOA,IAAI,CAACN,KAAD,CAAX;AAEZQ,EAAAA,OAAO,CAACC,KAAR,CACE,gGADF;AAIA,SAAO,IAAP;AACD;;;;"}