{"version": 3, "file": "IconBase.esm.js", "sources": ["../../src/lib/IconBase.tsx"], "sourcesContent": ["import React, { forwardRef, useContext } from \"react\";\nimport { IconProps, IconWeight, IconContext } from \"../lib\";\n\nexport type RenderFunction = (\n  weight: IconWeight,\n  color: string\n) => React.ReactNode | null;\n\ninterface IconBaseProps extends IconProps {\n  renderPath: RenderFunction;\n}\n\nconst IconBase = forwardRef<SVGSVGElement, IconBaseProps>((props, ref) => {\n  const {\n    alt,\n    color,\n    size,\n    weight,\n    mirrored,\n    children,\n    renderPath,\n    ...restProps\n  } = props;\n\n  const {\n    color: contextColor = \"currentColor\",\n    size: contextSize,\n    weight: contextWeight = \"regular\",\n    mirrored: contextMirrored = false,\n    ...restContext\n  } = useContext(IconContext);\n\n  return (\n    <svg\n      ref={ref}\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width={size ?? contextSize}\n      height={size ?? contextSize}\n      fill={color ?? contextColor}\n      viewBox=\"0 0 256 256\"\n      transform={mirrored || contextMirrored ? \"scale(-1, 1)\" : undefined}\n      {...restContext}\n      {...restProps}\n    >\n      {!!alt && <title>{alt}</title>}\n      {children}\n      <rect width=\"256\" height=\"256\" fill=\"none\" />\n      {renderPath(weight ?? contextWeight, color ?? contextColor)}\n    </svg>\n  );\n});\n\nIconBase.displayName = \"IconBase\";\n\nexport default IconBase;\n"], "names": ["IconBase", "forwardRef", "props", "ref", "alt", "color", "size", "weight", "mirrored", "children", "<PERSON><PERSON><PERSON>", "restProps", "useContext", "IconContext", "contextColor", "contextSize", "contextWeight", "contextMirrored", "restContext", "React", "xmlns", "width", "height", "fill", "viewBox", "transform", "undefined", "displayName"], "mappings": ";;;;AAYA,IAAMA,QAAQ,gBAAGC,UAAU,CAA+B,UAACC,KAAD,EAAQC,GAAR;MAEtDC,MAQEF,MARFE;MACAC,QAOEH,MAPFG;MACAC,OAMEJ,MANFI;MACAC,SAKEL,MALFK;MACAC,WAIEN,MAJFM;MACAC,WAGEP,MAHFO;MACAC,aAEER,MAFFQ;MACGC,0CACDT;;oBAQAU,UAAU,CAACC,WAAD;sCALZR;MAAOS,8CAAe;MAChBC,0BAANT;uCACAC;MAAQS,gDAAgB;yCACxBR;MAAUS,oDAAkB;MACzBC;;AAGL,SACEC,mBAAA,MAAA;AACEhB,IAAAA,GAAG,EAAEA;AACLiB,IAAAA,KAAK,EAAC;AACNC,IAAAA,KAAK,EAAEf,IAAF,WAAEA,IAAF,GAAUS;AACfO,IAAAA,MAAM,EAAEhB,IAAF,WAAEA,IAAF,GAAUS;AAChBQ,IAAAA,IAAI,EAAElB,KAAF,WAAEA,KAAF,GAAWS;AACfU,IAAAA,OAAO,EAAC;AACRC,IAAAA,SAAS,EAAEjB,QAAQ,IAAIS,eAAZ,GAA8B,cAA9B,GAA+CS;KACtDR,aACAP,UATN,EAWG,CAAC,CAACP,GAAF,IAASe,mBAAA,QAAA,MAAA,EAAQf,GAAR,CAXZ,EAYGK,QAZH,EAaEU,mBAAA,OAAA;AAAME,IAAAA,KAAK,EAAC;AAAMC,IAAAA,MAAM,EAAC;AAAMC,IAAAA,IAAI,EAAC;GAApC,CAbF,EAcGb,UAAU,CAACH,MAAD,WAACA,MAAD,GAAWS,aAAX,EAA0BX,KAA1B,WAA0BA,KAA1B,GAAmCS,YAAnC,CAdb,CADF;AAkBD,CAtC0B,CAA3B;AAwCAd,QAAQ,CAAC2B,WAAT,GAAuB,UAAvB;;;;"}