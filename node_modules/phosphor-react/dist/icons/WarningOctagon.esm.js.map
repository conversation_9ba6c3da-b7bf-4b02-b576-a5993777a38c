{"version": 3, "file": "WarningOctagon.esm.js", "sources": ["../../src/icons/WarningOctagon.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <line\n      x1=\"128\"\n      y1=\"80\"\n      x2=\"128\"\n      y2=\"132\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M164.5,32h-73a7.9,7.9,0,0,0-5.6,2.3L34.3,85.9A7.9,7.9,0,0,0,32,91.5v73a7.9,7.9,0,0,0,2.3,5.6l51.6,51.6a7.9,7.9,0,0,0,5.6,2.3h73a7.9,7.9,0,0,0,5.6-2.3l51.6-51.6a7.9,7.9,0,0,0,2.3-5.6v-73a7.9,7.9,0,0,0-2.3-5.6L170.1,34.3A7.9,7.9,0,0,0,164.5,32Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <circle cx=\"128\" cy=\"172\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M164.5,32h-73a7.9,7.9,0,0,0-5.6,2.3L34.3,85.9A7.9,7.9,0,0,0,32,91.5v73a7.9,7.9,0,0,0,2.3,5.6l51.6,51.6a7.9,7.9,0,0,0,5.6,2.3h73a7.9,7.9,0,0,0,5.6-2.3l51.6-51.6a7.9,7.9,0,0,0,2.3-5.6v-73a7.9,7.9,0,0,0-2.3-5.6L170.1,34.3A7.9,7.9,0,0,0,164.5,32Z\"\n      opacity=\"0.2\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"80\"\n      x2=\"128\"\n      y2=\"136\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M164.5,32h-73a7.9,7.9,0,0,0-5.6,2.3L34.3,85.9A7.9,7.9,0,0,0,32,91.5v73a7.9,7.9,0,0,0,2.3,5.6l51.6,51.6a7.9,7.9,0,0,0,5.6,2.3h73a7.9,7.9,0,0,0,5.6-2.3l51.6-51.6a7.9,7.9,0,0,0,2.3-5.6v-73a7.9,7.9,0,0,0-2.3-5.6L170.1,34.3A7.9,7.9,0,0,0,164.5,32Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeMiterlimit=\"10\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"172\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M227.3,80.2,175.8,28.7A16.1,16.1,0,0,0,164.5,24h-73a16.1,16.1,0,0,0-11.3,4.7L28.7,80.2A16.1,16.1,0,0,0,24,91.5v73a16.1,16.1,0,0,0,4.7,11.3l51.5,51.5A16.1,16.1,0,0,0,91.5,232h73a16.1,16.1,0,0,0,11.3-4.7l51.5-51.5a16.1,16.1,0,0,0,4.7-11.3v-73A16.1,16.1,0,0,0,227.3,80.2ZM120,80a8,8,0,0,1,16,0v56a8,8,0,0,1-16,0Zm8,104a12,12,0,1,1,12-12A12,12,0,0,1,128,184Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <line\n      x1=\"128\"\n      y1=\"80\"\n      x2=\"128\"\n      y2=\"136\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M164.5,32h-73a7.9,7.9,0,0,0-5.6,2.3L34.3,85.9A7.9,7.9,0,0,0,32,91.5v73a7.9,7.9,0,0,0,2.3,5.6l51.6,51.6a7.9,7.9,0,0,0,5.6,2.3h73a7.9,7.9,0,0,0,5.6-2.3l51.6-51.6a7.9,7.9,0,0,0,2.3-5.6v-73a7.9,7.9,0,0,0-2.3-5.6L170.1,34.3A7.9,7.9,0,0,0,164.5,32Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <circle cx=\"128\" cy=\"172\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <line\n      x1=\"128\"\n      y1=\"80\"\n      x2=\"128\"\n      y2=\"136\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M164.5,32h-73a7.9,7.9,0,0,0-5.6,2.3L34.3,85.9A7.9,7.9,0,0,0,32,91.5v73a7.9,7.9,0,0,0,2.3,5.6l51.6,51.6a7.9,7.9,0,0,0,5.6,2.3h73a7.9,7.9,0,0,0,5.6-2.3l51.6-51.6a7.9,7.9,0,0,0,2.3-5.6v-73a7.9,7.9,0,0,0-2.3-5.6L170.1,34.3A7.9,7.9,0,0,0,164.5,32Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <circle cx=\"128\" cy=\"172\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <line\n      x1=\"128\"\n      y1=\"80\"\n      x2=\"128\"\n      y2=\"136\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M164.5,32h-73a7.9,7.9,0,0,0-5.6,2.3L34.3,85.9A7.9,7.9,0,0,0,32,91.5v73a7.9,7.9,0,0,0,2.3,5.6l51.6,51.6a7.9,7.9,0,0,0,5.6,2.3h73a7.9,7.9,0,0,0,5.6-2.3l51.6-51.6a7.9,7.9,0,0,0,2.3-5.6v-73a7.9,7.9,0,0,0-2.3-5.6L170.1,34.3A7.9,7.9,0,0,0,164.5,32Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeMiterlimit=\"10\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"172\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst WarningOctagon = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nWarningOctagon.displayName = \"WarningOctagon\";\n\nexport default WarningOctagon;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "x1", "y1", "x2", "y2", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "cx", "cy", "r", "opacity", "strokeMiterlimit", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "WarningOctagon", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAZF,EAoBET,mBAAA,SAAA;AAAQW,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CApBF,CADwB;AAAA,CAA1B;AAyBAjB,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFI,IAAAA,OAAO,EAAC;GAFV,CADF,EAKEd,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CALF,EAgBET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRgB,IAAAA,gBAAgB,EAAC;AACjBN,IAAAA,WAAW,EAAC;GALd,CAhBF,EAuBET,mBAAA,SAAA;AAAQW,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAvBF,CAD2B;AAAA,CAA7B;AA4BAjB,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMU,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAd,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAZF,EAoBET,mBAAA,SAAA;AAAQW,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CApBF,CADyB;AAAA,CAA3B;AAyBAjB,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAZF,EAoBET,mBAAA,SAAA;AAAQW,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CApBF,CADwB;AAAA,CAA1B;AAyBAjB,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRgB,IAAAA,gBAAgB,EAAC;AACjBN,IAAAA,WAAW,EAAC;GALd,CAZF,EAmBET,mBAAA,SAAA;AAAQW,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAnBF,CAD2B;AAAA,CAA7B;;AAwBA,IAAMG,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBlB,KAArB;AAAA,SACjCmB,mBAAmB,CAACD,MAAD,EAASlB,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMuB,cAAc,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAC1DtB,mBAAA,CAACuB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CAD0D;AAAA,CAA3B,CAAjC;AAIAG,cAAc,CAACK,WAAf,GAA6B,gBAA7B;;;;"}