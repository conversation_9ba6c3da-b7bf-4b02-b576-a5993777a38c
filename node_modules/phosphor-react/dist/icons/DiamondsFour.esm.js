import React, { forwardRef } from 'react';
import { renderPathForWeight } from '../lib/index.esm.js';
import IconBase from '../lib/IconBase.esm.js';

/* GENERATED FILE */
var pathsByWeight = /*#__PURE__*/new Map();
pathsByWeight.set("bold", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "101.1",
    y: "35.1",
    width: "53.7",
    height: "53.74",
    rx: "8",
    transform: "translate(-6.4 108.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }), React.createElement("rect", {
    x: "167.1",
    y: "101.1",
    width: "53.7",
    height: "53.74",
    rx: "8",
    transform: "translate(-33.7 174.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }), React.createElement("rect", {
    x: "35.1",
    y: "101.1",
    width: "53.7",
    height: "53.74",
    rx: "8",
    transform: "translate(-72.4 81.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }), React.createElement("rect", {
    x: "101.1",
    y: "167.1",
    width: "53.7",
    height: "53.74",
    rx: "8",
    transform: "translate(-99.7 147.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }));
});
pathsByWeight.set("duotone", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "99.7",
    y: "35.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-7.8 109.3) rotate(-45)",
    opacity: "0.2"
  }), React.createElement("rect", {
    x: "163.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-34.3 173.3) rotate(-45)",
    opacity: "0.2"
  }), React.createElement("rect", {
    x: "35.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-71.8 82.7) rotate(-45)",
    opacity: "0.2"
  }), React.createElement("rect", {
    x: "99.7",
    y: "163.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-98.3 146.7) rotate(-45)",
    opacity: "0.2"
  }), React.createElement("rect", {
    x: "99.7",
    y: "35.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-7.8 109.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("rect", {
    x: "163.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-34.3 173.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("rect", {
    x: "35.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-71.8 82.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("rect", {
    x: "99.7",
    y: "163.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-98.3 146.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }));
});
pathsByWeight.set("fill", function () {
  return React.createElement(React.Fragment, null, React.createElement("path", {
    d: "M88,75.3a16.1,16.1,0,0,1,0-22.6L116.7,24a15.9,15.9,0,0,1,22.6,0L168,52.7a16.1,16.1,0,0,1,0,22.6L139.3,104a15.9,15.9,0,0,1-22.6,0Zm144,41.4L203.3,88a15.9,15.9,0,0,0-22.6,0L152,116.7a16.1,16.1,0,0,0,0,22.6L180.7,168a15.9,15.9,0,0,0,22.6,0L232,139.3A16.1,16.1,0,0,0,232,116.7Zm-128,0L75.3,88a15.9,15.9,0,0,0-22.6,0L24,116.7a16.1,16.1,0,0,0,0,22.6L52.7,168a15.9,15.9,0,0,0,22.6,0L104,139.3A16.1,16.1,0,0,0,104,116.7ZM139.3,152a15.9,15.9,0,0,0-22.6,0L88,180.7a16.1,16.1,0,0,0,0,22.6L116.7,232a15.9,15.9,0,0,0,22.6,0L168,203.3a16.1,16.1,0,0,0,0-22.6Z"
  }));
});
pathsByWeight.set("light", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "99.7",
    y: "35.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-7.8 109.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }), React.createElement("rect", {
    x: "163.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-34.3 173.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }), React.createElement("rect", {
    x: "35.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-71.8 82.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }), React.createElement("rect", {
    x: "99.7",
    y: "163.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-98.3 146.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }));
});
pathsByWeight.set("thin", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "99.7",
    y: "35.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-7.8 109.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }), React.createElement("rect", {
    x: "163.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-34.3 173.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }), React.createElement("rect", {
    x: "35.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-71.8 82.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }), React.createElement("rect", {
    x: "99.7",
    y: "163.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-98.3 146.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }));
});
pathsByWeight.set("regular", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "99.7",
    y: "35.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-7.8 109.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("rect", {
    x: "163.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-34.3 173.3) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("rect", {
    x: "35.7",
    y: "99.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-71.8 82.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("rect", {
    x: "99.7",
    y: "163.7",
    width: "56.6",
    height: "56.57",
    rx: "8",
    transform: "translate(-98.3 146.7) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }));
});

var renderPath = function renderPath(weight, color) {
  return renderPathForWeight(weight, color, pathsByWeight);
};

var DiamondsFour = /*#__PURE__*/forwardRef(function (props, ref) {
  return React.createElement(IconBase, Object.assign({
    ref: ref
  }, props, {
    renderPath: renderPath
  }));
});
DiamondsFour.displayName = "DiamondsFour";

export default DiamondsFour;
//# sourceMappingURL=DiamondsFour.esm.js.map
