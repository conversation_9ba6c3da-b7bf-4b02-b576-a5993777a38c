{"version": 3, "file": "ForkKnife.esm.js", "sources": ["../../src/icons/ForkKnife.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <line\n      x1=\"84\"\n      y1=\"32\"\n      x2=\"84\"\n      y2=\"72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"84\"\n      y1=\"116\"\n      x2=\"84\"\n      y2=\"224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M120,32V80a36,36,0,0,1-72,0V32\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M204,160H148S160,48,204,32V224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path d=\"M204,160H148S160,48,204,32\" opacity=\"0.2\" />\n    <line\n      x1=\"84\"\n      y1=\"32\"\n      x2=\"84\"\n      y2=\"72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"84\"\n      y1=\"116\"\n      x2=\"84\"\n      y2=\"224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M112,32l8,48a36,36,0,0,1-72,0l8-48\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M204,160H148S160,48,204,32V224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M212,32V224a8,8,0,0,1-16,0V168H148a8.2,8.2,0,0,1-6-2.7,8.1,8.1,0,0,1-2-6.2,412.8,412.8,0,0,1,11.8-59.3c12-42.4,28.7-67.8,49.5-75.3A7.9,7.9,0,0,1,212,32ZM127.9,78.7l-8-48a8,8,0,1,0-15.8,2.6L110.6,72H92V32a8,8,0,0,0-16,0V72H57.4l6.5-38.7a8,8,0,1,0-15.8-2.6l-8,48h0A4.9,4.9,0,0,0,40,80a44.1,44.1,0,0,0,36,43.3V224a8,8,0,0,0,16,0V123.3A44.1,44.1,0,0,0,128,80a4.9,4.9,0,0,0-.1-1.2Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <line\n      x1=\"84\"\n      y1=\"32\"\n      x2=\"84\"\n      y2=\"72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"84\"\n      y1=\"116\"\n      x2=\"84\"\n      y2=\"224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M112,32l8,48a36,36,0,0,1-72,0l8-48\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M204,160H148S160,48,204,32V224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <line\n      x1=\"84\"\n      y1=\"32\"\n      x2=\"84\"\n      y2=\"72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"84\"\n      y1=\"116\"\n      x2=\"84\"\n      y2=\"224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M112,32l8,48a36,36,0,0,1-72,0l8-48\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M204,160H148S160,48,204,32V224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <line\n      x1=\"84\"\n      y1=\"32\"\n      x2=\"84\"\n      y2=\"72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"84\"\n      y1=\"116\"\n      x2=\"84\"\n      y2=\"224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M112,32l8,48a36,36,0,0,1-72,0l8-48\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M204,160H148S160,48,204,32V224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst ForkKnife = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nForkKnife.displayName = \"ForkKnife\";\n\nexport default ForkKnife;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "x1", "y1", "x2", "y2", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "opacity", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "ForkKnife", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAZF,EAuBET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAvBF,EA+BET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA/BF,CADwB;AAAA,CAA1B;AA2CAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMU,IAAAA,CAAC,EAAC;AAA6BC,IAAAA,OAAO,EAAC;GAA7C,CADF,EAEEX,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAFF,EAaET,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAbF,EAwBET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAxBF,EAgCET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAhCF,CAD2B;AAAA,CAA7B;AA4CAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMU,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAd,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAZF,EAuBET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAvBF,EA+BET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA/BF,CADyB;AAAA,CAA3B;AA2CAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAZF,EAuBET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAvBF,EA+BET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA/BF,CADwB;AAAA,CAA1B;AA2CAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAZF,EAuBET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAvBF,EA+BET,mBAAA,OAAA;AACEU,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA/BF,CAD2B;AAAA,CAA7B;;AA2CA,IAAMG,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBd,KAArB;AAAA,SACjCe,mBAAmB,CAACD,MAAD,EAASd,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMmB,SAAS,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACrDlB,mBAAA,CAACmB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADqD;AAAA,CAA3B,CAA5B;AAIAG,SAAS,CAACK,WAAV,GAAwB,WAAxB;;;;"}