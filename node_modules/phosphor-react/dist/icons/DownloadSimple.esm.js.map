{"version": 3, "file": "DownloadSimple.esm.js", "sources": ["../../src/icons/DownloadSimple.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <polyline\n      points=\"86 110 128 152 170 110\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"40\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M216,152v56a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <polyline\n      points=\"86 110 128 152 170 110\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"40\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M216,152v56a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M80.3,115.7a8.2,8.2,0,0,1-1.7-8.7,8,8,0,0,1,7.4-5h34V40a8,8,0,0,1,16,0v62h34a8,8,0,0,1,7.4,5,8.2,8.2,0,0,1-1.7,8.7l-42,42a8.2,8.2,0,0,1-11.4,0ZM216,144a8,8,0,0,0-8,8v56H48V152a8,8,0,0,0-16,0v56a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V152A8,8,0,0,0,216,144Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <polyline\n      points=\"86 110 128 152 170 110\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"40\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M216,152v56a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <polyline\n      points=\"86 110 128 152 170 110\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"40\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M216,152v56a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <polyline\n      points=\"86 110 128 152 170 110\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"40\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M216,152v56a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst DownloadSimple = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nDownloadSimple.displayName = \"DownloadSimple\";\n\nexport default DownloadSimple;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "points", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "x1", "y1", "x2", "y2", "d", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "DownloadSimple", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEO,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CATF,EAoBEN,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CApBF,CADwB;AAAA,CAA1B;AAgCAV,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEO,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CATF,EAoBEN,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CApBF,CAD2B;AAAA,CAA7B;AAgCAV,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMW,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAf,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEO,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CATF,EAoBEN,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CApBF,CADyB;AAAA,CAA3B;AAgCAV,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEO,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CATF,EAoBEN,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CApBF,CADwB;AAAA,CAA1B;AAgCAV,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEO,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CATF,EAoBEN,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CApBF,CAD2B;AAAA,CAA7B;;AAgCA,IAAMM,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBd,KAArB;AAAA,SACjCe,mBAAmB,CAACD,MAAD,EAASd,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMmB,cAAc,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAC1DlB,mBAAA,CAACmB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CAD0D;AAAA,CAA3B,CAAjC;AAIAG,cAAc,CAACK,WAAf,GAA6B,gBAA7B;;;;"}