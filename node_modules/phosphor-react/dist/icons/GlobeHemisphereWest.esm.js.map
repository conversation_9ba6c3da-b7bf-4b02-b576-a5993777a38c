{"version": 3, "file": "GlobeHemisphereWest.esm.js", "sources": ["../../src/icons/GlobeHemisphereWest.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M214.2,170.4,159.9,137a7.2,7.2,0,0,0-3.1-1.1l-22.9-3.1a7.8,7.8,0,0,0-8.3,4.7l-13.7,30.7a8,8,0,0,0,1.4,8.7l18.8,20.3a8.2,8.2,0,0,1,2,7L130.2,224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M65,55.6,56,76.8a8,8,0,0,0-.1,5.9l11.5,30.6a7.9,7.9,0,0,0,5.8,5.1L94.6,123a7.8,7.8,0,0,1,5.5,4.3l3.8,7.9a8.2,8.2,0,0,0,7.2,4.5h13.5\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M152.5,35.1l9.3,16.8a8.1,8.1,0,0,1-1.6,9.8L133.3,86a7.2,7.2,0,0,1-1.5,1.1l-12.3,6.8a8.6,8.6,0,0,1-3.8,1H94.3a8.1,8.1,0,0,0-7.4,4.9l-8.3,19.7\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M130.2,224a96.3,96.3,0,0,0,84-53.6h0L159.9,137a7.2,7.2,0,0,0-3.1-1.1l-22.9-3.1a7.8,7.8,0,0,0-8.3,4.7l-13.7,30.7a8,8,0,0,0,1.4,8.7l18.8,20.3a8.2,8.2,0,0,1,2,7L130.2,224Z\"\n      opacity=\"0.2\"\n    />\n    <path\n      d=\"M65,55.6,56,76.8a8,8,0,0,0-.1,5.9l11.5,30.6a7.9,7.9,0,0,0,5.8,5.1l5.4,1.1,8.3-19.7a8.1,8.1,0,0,1,7.4-4.9h21.4a8.6,8.6,0,0,0,3.8-1l12.3-6.8a7.2,7.2,0,0,0,1.5-1.1l26.9-24.3a8.1,8.1,0,0,0,1.6-9.8l-9.3-16.8h0A98.5,98.5,0,0,0,128,32,95.4,95.4,0,0,0,65,55.6Z\"\n      opacity=\"0.2\"\n    />\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M214.2,170.4,159.9,137a7.2,7.2,0,0,0-3.1-1.1l-22.9-3.1a7.8,7.8,0,0,0-8.3,4.7l-13.7,30.7a8,8,0,0,0,1.4,8.7l18.8,20.3a8.2,8.2,0,0,1,2,7L130.2,224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M65,55.6,56,76.8a8,8,0,0,0-.1,5.9l11.5,30.6a7.9,7.9,0,0,0,5.8,5.1L94.6,123a7.8,7.8,0,0,1,5.5,4.3l3.8,7.9a8.2,8.2,0,0,0,7.2,4.5h13.5\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M152.5,35.1l9.3,16.8a8.1,8.1,0,0,1-1.6,9.8L133.3,86a7.2,7.2,0,0,1-1.5,1.1l-12.3,6.8a8.6,8.6,0,0,1-3.8,1H94.3a8.1,8.1,0,0,0-7.4,4.9l-8.3,19.7\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M221.6,173.3A102.9,102.9,0,0,0,232,128,104.2,104.2,0,0,0,154.8,27.5h-.5A103.8,103.8,0,0,0,60.4,49l-1.3,1.2A103.9,103.9,0,0,0,128,232h2.4A104.3,104.3,0,0,0,221,174.6h0ZM216,128a89.3,89.3,0,0,1-5.5,30.7l-46.4-28.5a16,16,0,0,0-6.3-2.3l-22.8-3a16.1,16.1,0,0,0-15.3,6.8h-8.6l-3.8-7.9a16.2,16.2,0,0,0-11-8.7l-6.6-1.4,2.5-5.9a8.1,8.1,0,0,1,7.4-4.9h16.1a16.1,16.1,0,0,0,7.7-2l12.2-6.8a16.1,16.1,0,0,0,3-2.1l26.9-24.4A15.7,15.7,0,0,0,170,50.7,88,88,0,0,1,216,128ZM40,128a87.1,87.1,0,0,1,9.5-39.7l10.4,27.9a16.1,16.1,0,0,0,11.6,10l5.5,1.2h.1l12,2.6a7.8,7.8,0,0,1,5.5,4.3l2.1,4.4a16.1,16.1,0,0,0,14.4,9h1.2l-7.7,17.2a15.9,15.9,0,0,0,2.8,17.4l16.1,17.4a8.3,8.3,0,0,1,2,6.9l-1.8,9.3A88.1,88.1,0,0,1,40,128Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M214.2,170.4,159.9,137a7.2,7.2,0,0,0-3.1-1.1l-22.9-3.1a7.8,7.8,0,0,0-8.3,4.7l-13.7,30.7a8,8,0,0,0,1.4,8.7l18.8,20.3a8.2,8.2,0,0,1,2,7L130.2,224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M65,55.6,56,76.8a8,8,0,0,0-.1,5.9l11.5,30.6a7.9,7.9,0,0,0,5.8,5.1L94.6,123a7.8,7.8,0,0,1,5.5,4.3l3.8,7.9a8.2,8.2,0,0,0,7.2,4.5h13.5\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M152.5,35.1l9.3,16.8a8.1,8.1,0,0,1-1.6,9.8L133.3,86a7.2,7.2,0,0,1-1.5,1.1l-12.3,6.8a8.6,8.6,0,0,1-3.8,1H94.3a8.1,8.1,0,0,0-7.4,4.9l-8.3,19.7\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M214.2,170.4,159.9,137a7.2,7.2,0,0,0-3.1-1.1l-22.9-3.1a7.8,7.8,0,0,0-8.3,4.7l-13.7,30.7a8,8,0,0,0,1.4,8.7l18.8,20.3a8.2,8.2,0,0,1,2,7L130.2,224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M65,55.6,56,76.8a8,8,0,0,0-.1,5.9l11.5,30.6a7.9,7.9,0,0,0,5.8,5.1L94.6,123a7.8,7.8,0,0,1,5.5,4.3l3.8,7.9a8.2,8.2,0,0,0,7.2,4.5h13.5\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M152.5,35.1l9.3,16.8a8.1,8.1,0,0,1-1.6,9.8L133.3,86a7.2,7.2,0,0,1-1.5,1.1l-12.3,6.8a8.6,8.6,0,0,1-3.8,1H94.3a8.1,8.1,0,0,0-7.4,4.9l-8.3,19.7\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M214.2,170.4,159.9,137a7.2,7.2,0,0,0-3.1-1.1l-22.9-3.1a7.8,7.8,0,0,0-8.3,4.7l-13.7,30.7a8,8,0,0,0,1.4,8.7l18.8,20.3a8.2,8.2,0,0,1,2,7L130.2,224\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M65,55.6,56,76.8a8,8,0,0,0-.1,5.9l11.5,30.6a7.9,7.9,0,0,0,5.8,5.1L94.6,123a7.8,7.8,0,0,1,5.5,4.3l3.8,7.9a8.2,8.2,0,0,0,7.2,4.5h13.5\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M152.5,35.1l9.3,16.8a8.1,8.1,0,0,1-1.6,9.8L133.3,86a7.2,7.2,0,0,1-1.5,1.1l-12.3,6.8a8.6,8.6,0,0,1-3.8,1H94.3a8.1,8.1,0,0,0-7.4,4.9l-8.3,19.7\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst GlobeHemisphereWest = forwardRef<SVGSVGElement, IconProps>(\n  (props, ref) => <IconBase ref={ref} {...props} renderPath={renderPath} />\n);\n\nGlobeHemisphereWest.displayName = \"GlobeHemisphereWest\";\n\nexport default GlobeHemisphereWest;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "cx", "cy", "r", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "opacity", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "GlobeHemisphereWest", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GARd,CADF,EAWER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAXF,EAmBER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAnBF,EA2BER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA3BF,CADwB;AAAA,CAA1B;AAuCAZ,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFC,IAAAA,OAAO,EAAC;GAFV,CADF,EAKEV,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFC,IAAAA,OAAO,EAAC;GAFV,CALF,EASEV,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GARd,CATF,EAmBER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAnBF,EA2BER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA3BF,EAmCER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAnCF,CAD2B;AAAA,CAA7B;AA+CAZ,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMS,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAb,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GARd,CADF,EAWER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAXF,EAmBER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAnBF,EA2BER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA3BF,CADyB;AAAA,CAA3B;AAuCAZ,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GARd,CADF,EAWER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAXF,EAmBER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAnBF,EA2BER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA3BF,CADwB;AAAA,CAA1B;AAuCAZ,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GARd,CADF,EAWER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAXF,EAmBER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAnBF,EA2BER,mBAAA,OAAA;AACES,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CA3BF,CAD2B;AAAA,CAA7B;;AAuCA,IAAMG,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBb,KAArB;AAAA,SACjCc,mBAAmB,CAACD,MAAD,EAASb,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMkB,mBAAmB,gBAAGC,UAAU,CACpC,UAACC,KAAD,EAAQC,GAAR;AAAA,SAAgBjB,mBAAA,CAACkB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CAAhB;AAAA,CADoC,CAAtC;AAIAG,mBAAmB,CAACK,WAApB,GAAkC,qBAAlC;;;;"}