{"version": 3, "file": "BracketsSquare.esm.js", "sources": ["../../src/icons/BracketsSquare.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <polyline\n      points=\"80 40 40 40 40 216 80 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <polyline\n      points=\"176 40 216 40 216 216 176 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <polyline\n      points=\"80 40 40 40 40 216 80 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <polyline\n      points=\"176 40 216 40 216 216 176 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M48,48V208H80a8,8,0,0,1,0,16H40a8,8,0,0,1-8-8V40a8,8,0,0,1,8-8H80a8,8,0,0,1,0,16ZM216,32H176a8,8,0,0,0,0,16h32V208H176a8,8,0,0,0,0,16h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <polyline\n      points=\"80 40 40 40 40 216 80 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <polyline\n      points=\"176 40 216 40 216 216 176 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <polyline\n      points=\"80 40 40 40 40 216 80 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <polyline\n      points=\"176 40 216 40 216 216 176 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <polyline\n      points=\"80 40 40 40 40 216 80 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <polyline\n      points=\"176 40 216 40 216 216 176 216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst BracketsSquare = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nBracketsSquare.displayName = \"BracketsSquare\";\n\nexport default BracketsSquare;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "points", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "BracketsSquare", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CADwB;AAAA,CAA1B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CAD2B;AAAA,CAA7B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMO,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAX,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CADyB;AAAA,CAA3B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CADwB;AAAA,CAA1B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,WAAA;AACEC,IAAAA,MAAM,EAAC;AACPC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CAD2B;AAAA,CAA7B;;AAqBA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBV,KAArB;AAAA,SACjCW,mBAAmB,CAACD,MAAD,EAASV,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMe,cAAc,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAC1Dd,mBAAA,CAACe,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CAD0D;AAAA,CAA3B,CAAjC;AAIAG,cAAc,CAACK,WAAf,GAA6B,gBAA7B;;;;"}