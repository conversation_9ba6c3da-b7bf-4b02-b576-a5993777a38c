{"version": 3, "file": "LinkSimpleBreak.esm.js", "sources": ["../../src/icons/LinkSimpleBreak.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <path\n      d=\"M145,178.9l-28.3,28.3a48,48,0,0,1-67.9-67.9L77.1,111\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M178.9,145l28.3-28.3a48,48,0,0,0-67.9-67.9L111,77.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M145,178.9l-28.3,28.3a48,48,0,0,1-67.9-67.9L77.1,111\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M178.9,145l28.3-28.3a48,48,0,0,0-67.9-67.9L111,77.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M139.3,173.3,111,201.5a39.7,39.7,0,0,1-56.5,0,40,40,0,0,1,0-56.5l28.2-28.3a8,8,0,0,0-11.3-11.3L43.1,133.7a56,56,0,0,0,79.2,79.2l28.3-28.3a8,8,0,0,0,0-11.3A7.9,7.9,0,0,0,139.3,173.3Z\" />\n    <path d=\"M212.9,43.1a56,56,0,0,0-79.2,0L105.4,71.4a8,8,0,0,0,11.3,11.3L145,54.5a39.7,39.7,0,0,1,56.5,0,40,40,0,0,1,0,56.5l-28.2,28.3a7.9,7.9,0,0,0,0,11.3,7.7,7.7,0,0,0,5.6,2.4,8,8,0,0,0,5.7-2.4l28.3-28.3A56.1,56.1,0,0,0,212.9,43.1Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <path\n      d=\"M145,178.9l-28.3,28.3a48,48,0,0,1-67.9-67.9L77.1,111\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M178.9,145l28.3-28.3a48,48,0,0,0-67.9-67.9L111,77.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <path\n      d=\"M145,178.9l-28.3,28.3a48,48,0,0,1-67.9-67.9L77.1,111\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M178.9,145l28.3-28.3a48,48,0,0,0-67.9-67.9L111,77.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <path\n      d=\"M145,178.9l-28.3,28.3a48,48,0,0,1-67.9-67.9L77.1,111\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M178.9,145l28.3-28.3a48,48,0,0,0-67.9-67.9L111,77.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M145,178.9l-28.3,28.3a48,48,0,0,1-67.9-67.9L77.1,111\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M178.9,145l28.3-28.3a48,48,0,0,0-67.9-67.9L111,77.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst LinkSimpleBreak = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nLinkSimpleBreak.displayName = \"LinkSimpleBreak\";\n\nexport default LinkSimpleBreak;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "d", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "LinkSimpleBreak", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CADwB;AAAA,CAA1B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CAD2B;AAAA,CAA7B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMC,IAAAA,CAAC,EAAC;GAAR,CADF,EAEED,mBAAA,OAAA;AAAMC,IAAAA,CAAC,EAAC;GAAR,CAFF,CADwB;AAAA,CAA1B;AAOAL,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CADyB;AAAA,CAA3B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CADwB;AAAA,CAA1B;AAqBAV,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,EAiBEN,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAjBF,EAyBEN,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAzBF,CAD2B;AAAA,CAA7B;;AAqCA,IAAMC,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBT,KAArB;AAAA,SACjCU,mBAAmB,CAACD,MAAD,EAAST,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMc,eAAe,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAC3Db,mBAAA,CAACc,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CAD2D;AAAA,CAA3B,CAAlC;AAIAG,eAAe,CAACK,WAAhB,GAA8B,iBAA9B;;;;"}