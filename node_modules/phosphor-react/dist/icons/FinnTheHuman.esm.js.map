{"version": 3, "file": "FinnTheHuman.esm.js", "sources": ["../../src/icons/FinnTheHuman.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <rect\n      x=\"56\"\n      y=\"100\"\n      width=\"144\"\n      height=\"80\"\n      rx=\"32\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M20,64a24,24,0,0,1,48,0H188a24,24,0,0,1,48,0v88a64.1,64.1,0,0,1-64,64H84a64.1,64.1,0,0,1-64-64Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <circle cx=\"96\" cy=\"140\" r=\"16\" />\n    <circle cx=\"160\" cy=\"140\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M208,40a23.9,23.9,0,0,0-24,24H72a24,24,0,0,0-48,0v80a64.1,64.1,0,0,0,64,64h80a64.1,64.1,0,0,0,64-64V64A23.9,23.9,0,0,0,208,40Zm-8,104a32,32,0,0,1-32,32H88a32,32,0,0,1-32-32v-8a32,32,0,0,1,32-32h80a32,32,0,0,1,32,32Z\"\n      opacity=\"0.2\"\n    />\n    <rect\n      x=\"56\"\n      y=\"104\"\n      width=\"144\"\n      height=\"72\"\n      rx=\"32\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M24,64a24,24,0,0,1,48,0H184a24,24,0,0,1,48,0v80a64.1,64.1,0,0,1-64,64H88a64.1,64.1,0,0,1-64-64Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"92\" cy=\"140\" r=\"12\" />\n    <circle cx=\"164\" cy=\"140\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M176,140a12,12,0,1,1-12-12A12,12,0,0,1,176,140ZM92,128a12,12,0,1,0,12,12A12,12,0,0,0,92,128ZM240,64v80a72.1,72.1,0,0,1-72,72H88a72.1,72.1,0,0,1-72-72V64a32,32,0,0,1,63-8h98a32,32,0,0,1,63,8Zm-40,72a32,32,0,0,0-32-32H88a32,32,0,0,0-32,32v8a32,32,0,0,0,32,32h80a32,32,0,0,0,32-32Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <rect\n      x=\"56\"\n      y=\"104\"\n      width=\"144\"\n      height=\"72\"\n      rx=\"32\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M24,64a24,24,0,0,1,48,0H184a24,24,0,0,1,48,0v80a64.1,64.1,0,0,1-64,64H88a64.1,64.1,0,0,1-64-64Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <circle cx=\"92\" cy=\"140\" r=\"10\" />\n    <circle cx=\"164\" cy=\"140\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <rect\n      x=\"56\"\n      y=\"104\"\n      width=\"144\"\n      height=\"72\"\n      rx=\"32\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M24,64a24,24,0,0,1,48,0H184a24,24,0,0,1,48,0v80a64.1,64.1,0,0,1-64,64H88a64.1,64.1,0,0,1-64-64Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <circle cx=\"92\" cy=\"140\" r=\"8\" />\n    <circle cx=\"164\" cy=\"140\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <rect\n      x=\"56\"\n      y=\"104\"\n      width=\"144\"\n      height=\"72\"\n      rx=\"32\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M24,64a24,24,0,0,1,48,0H184a24,24,0,0,1,48,0v80a64.1,64.1,0,0,1-64,64H88a64.1,64.1,0,0,1-64-64Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"92\" cy=\"140\" r=\"12\" />\n    <circle cx=\"164\" cy=\"140\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst FinnTheHuman = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nFinnTheHuman.displayName = \"FinnTheHuman\";\n\nexport default FinnTheHuman;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "x", "y", "width", "height", "rx", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "cx", "cy", "r", "opacity", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "FinnTheHuman", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CArBF,EAsBEd,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAtBF,CADwB;AAAA,CAA1B;AA2BAlB,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFI,IAAAA,OAAO,EAAC;GAFV,CADF,EAKEf,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CALF,EAiBEV,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAjBF,EAyBEV,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAzBF,EA0BEd,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CA1BF,CAD2B;AAAA,CAA7B;AA+BAlB,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMW,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAf,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CArBF,EAsBEd,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAtBF,CADyB;AAAA,CAA3B;AA2BAlB,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CArBF,EAsBEd,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAtBF,CADwB;AAAA,CAA1B;AA2BAlB,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,OAAA;AACEW,IAAAA,CAAC,EAAC;AACFL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CArBF,EAsBEd,mBAAA,SAAA;AAAQY,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAtBF,CAD2B;AAAA,CAA7B;;AA2BA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBlB,KAArB;AAAA,SACjCmB,mBAAmB,CAACD,MAAD,EAASlB,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMuB,YAAY,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACxDtB,mBAAA,CAACuB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADwD;AAAA,CAA3B,CAA/B;AAIAG,YAAY,CAACK,WAAb,GAA2B,cAA3B;;;;"}