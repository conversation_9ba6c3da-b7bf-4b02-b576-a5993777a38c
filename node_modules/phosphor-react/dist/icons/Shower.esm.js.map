{"version": 3, "file": "Shower.esm.js", "sources": ["../../src/icons/Shower.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <circle cx=\"60\" cy=\"228\" r=\"16\" />\n    <circle cx=\"92\" cy=\"196\" r=\"16\" />\n    <circle cx=\"28\" cy=\"196\" r=\"16\" />\n    <circle cx=\"60\" cy=\"164\" r=\"16\" />\n    <path\n      d=\"M244,40H219.3a7.9,7.9,0,0,0-5.6,2.3L184,72,64.4,86.1a8,8,0,0,0-4.7,13.6l96.6,96.6a8,8,0,0,0,13.6-4.7L184,72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M184,72,55.3,93.4A8,8,0,0,0,51,107l98,98a8,8,0,0,0,13.6-4.3Z\"\n      opacity=\"0.2\"\n    />\n    <circle cx=\"60\" cy=\"228\" r=\"12\" />\n    <circle cx=\"88\" cy=\"200\" r=\"12\" />\n    <circle cx=\"28\" cy=\"196\" r=\"12\" />\n    <circle cx=\"56\" cy=\"168\" r=\"12\" />\n    <path\n      d=\"M248,40H219.3a7.9,7.9,0,0,0-5.6,2.3L184,72,55.3,93.4A8,8,0,0,0,51,107l98,98a8,8,0,0,0,13.6-4.3L184,72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M72,228a12,12,0,1,1-12-12A12,12,0,0,1,72,228Zm16-40a12,12,0,1,0,12,12A12,12,0,0,0,88,188Zm-60-4a12,12,0,1,0,12,12A12,12,0,0,0,28,184Zm40-16a12,12,0,1,0-12,12A12,12,0,0,0,68,168ZM248,32H219.3A15.9,15.9,0,0,0,208,36.7L180.2,64.5,54,85.6A15.7,15.7,0,0,0,41.4,96.5a16,16,0,0,0,3.9,16.1l98.1,98.1a16,16,0,0,0,11.2,4.7,16.2,16.2,0,0,0,4.9-.8A15.7,15.7,0,0,0,170.4,202L191.5,75.8h0L219.3,48H248a8,8,0,0,0,0-16Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <circle cx=\"60\" cy=\"228\" r=\"10\" />\n    <circle cx=\"88\" cy=\"200\" r=\"10\" />\n    <circle cx=\"28\" cy=\"196\" r=\"10\" />\n    <circle cx=\"56\" cy=\"168\" r=\"10\" />\n    <path\n      d=\"M248,40H219.3a7.9,7.9,0,0,0-5.6,2.3L184,72,55.3,93.4A8,8,0,0,0,51,107l98,98a8,8,0,0,0,13.6-4.3L184,72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <circle cx=\"60\" cy=\"228\" r=\"8\" />\n    <circle cx=\"88\" cy=\"200\" r=\"8\" />\n    <circle cx=\"28\" cy=\"196\" r=\"8\" />\n    <circle cx=\"56\" cy=\"168\" r=\"8\" />\n    <path\n      d=\"M248,40H219.3a7.9,7.9,0,0,0-5.6,2.3L184,72,55.3,93.4A8,8,0,0,0,51,107l98,98a8,8,0,0,0,13.6-4.3L184,72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <circle cx=\"60\" cy=\"228\" r=\"12\" />\n    <circle cx=\"88\" cy=\"200\" r=\"12\" />\n    <circle cx=\"28\" cy=\"196\" r=\"12\" />\n    <circle cx=\"56\" cy=\"168\" r=\"12\" />\n    <path\n      d=\"M248,40H219.3a7.9,7.9,0,0,0-5.6,2.3L184,72,55.3,93.4A8,8,0,0,0,51,107l98,98a8,8,0,0,0,13.6-4.3L184,72\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst Shower = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nShower.displayName = \"Shower\";\n\nexport default Shower;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "cx", "cy", "r", "d", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "opacity", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "Shower", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CALF,CADwB;AAAA,CAA1B;AAiBAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFM,IAAAA,OAAO,EAAC;GAFV,CADF,EAKEV,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CANF,EAOEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAPF,EAQEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CARF,EASEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CATF,CAD2B;AAAA,CAA7B;AAqBAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMI,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAR,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CALF,CADyB;AAAA,CAA3B;AAiBAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CALF,CADwB;AAAA,CAA1B;AAiBAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CALF,CAD2B;AAAA,CAA7B;;AAiBA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBb,KAArB;AAAA,SACjCc,mBAAmB,CAACD,MAAD,EAASb,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMkB,MAAM,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAClDjB,mBAAA,CAACkB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADkD;AAAA,CAA3B,CAAzB;AAIAG,MAAM,CAACK,WAAP,GAAqB,QAArB;;;;"}