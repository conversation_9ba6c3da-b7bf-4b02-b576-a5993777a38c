{"version": 3, "file": "Divide.esm.js", "sources": ["../../src/icons/Divide.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <line\n      x1=\"40\"\n      y1=\"128\"\n      x2=\"216\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <circle cx=\"128\" cy=\"64\" r=\"20\" />\n    <circle cx=\"128\" cy=\"192\" r=\"20\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <line\n      x1=\"40\"\n      y1=\"128\"\n      x2=\"216\"\n      y2=\"128\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"64\" r=\"16\" />\n    <circle cx=\"128\" cy=\"192\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM128,80a16,16,0,1,0-16-16A16,16,0,0,0,128,80Zm0,96a16,16,0,1,0,16,16A16,16,0,0,0,128,176Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <line\n      x1=\"40\"\n      y1=\"128\"\n      x2=\"216\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <circle cx=\"128\" cy=\"64\" r=\"14\" />\n    <circle cx=\"128\" cy=\"192\" r=\"14\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <line\n      x1=\"40\"\n      y1=\"128\"\n      x2=\"216\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <circle cx=\"128\" cy=\"64\" r=\"12\" />\n    <circle cx=\"128\" cy=\"192\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <line\n      x1=\"40\"\n      y1=\"128\"\n      x2=\"216\"\n      y2=\"128\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"64\" r=\"16\" />\n    <circle cx=\"128\" cy=\"192\" r=\"16\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst Divide = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nDivide.displayName = \"Divide\";\n\nexport default Divide;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "x1", "y1", "x2", "y2", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "cx", "cy", "r", "d", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "Divide", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAZF,EAaEZ,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAbF,CADwB;AAAA,CAA1B;AAkBAhB,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHE,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GARd,CADF,EAWET,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAXF,EAYEZ,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAZF,CAD2B;AAAA,CAA7B;AAiBAhB,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMa,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAjB,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAZF,EAaEZ,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAbF,CADyB;AAAA,CAA3B;AAkBAhB,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYET,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAZF,EAaEZ,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAbF,CADwB;AAAA,CAA1B;AAkBAhB,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHE,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GARd,CADF,EAWET,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAXF,EAYEZ,mBAAA,SAAA;AAAQU,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAZF,CAD2B;AAAA,CAA7B;;AAiBA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBhB,KAArB;AAAA,SACjCiB,mBAAmB,CAACD,MAAD,EAAShB,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMqB,MAAM,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAClDpB,mBAAA,CAACqB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADkD;AAAA,CAA3B,CAAzB;AAIAG,MAAM,CAACK,WAAP,GAAqB,QAArB;;;;"}