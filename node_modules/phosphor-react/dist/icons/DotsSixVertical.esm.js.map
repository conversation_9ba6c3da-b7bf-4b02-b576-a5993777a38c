{"version": 3, "file": "DotsSixVertical.esm.js", "sources": ["../../src/icons/DotsSixVertical.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", () => (\n  <>\n    <circle cx=\"92\" cy=\"60\" r=\"16\" />\n    <circle cx=\"164\" cy=\"60\" r=\"16\" />\n    <circle cx=\"92\" cy=\"128\" r=\"16\" />\n    <circle cx=\"164\" cy=\"128\" r=\"16\" />\n    <circle cx=\"92\" cy=\"196\" r=\"16\" />\n    <circle cx=\"164\" cy=\"196\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", () => (\n  <>\n    <circle cx=\"92\" cy=\"60\" r=\"12\" />\n    <circle cx=\"164\" cy=\"60\" r=\"12\" />\n    <circle cx=\"92\" cy=\"128\" r=\"12\" />\n    <circle cx=\"164\" cy=\"128\" r=\"12\" />\n    <circle cx=\"92\" cy=\"196\" r=\"12\" />\n    <circle cx=\"164\" cy=\"196\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M104,60A12,12,0,1,1,92,48,12,12,0,0,1,104,60Zm60,12a12,12,0,1,0-12-12A12,12,0,0,0,164,72ZM92,116a12,12,0,1,0,12,12A12,12,0,0,0,92,116Zm72,0a12,12,0,1,0,12,12A12,12,0,0,0,164,116ZM92,184a12,12,0,1,0,12,12A12,12,0,0,0,92,184Zm72,0a12,12,0,1,0,12,12A12,12,0,0,0,164,184Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", () => (\n  <>\n    <circle cx=\"92\" cy=\"60\" r=\"10\" />\n    <circle cx=\"164\" cy=\"60\" r=\"10\" />\n    <circle cx=\"92\" cy=\"128\" r=\"10\" />\n    <circle cx=\"164\" cy=\"128\" r=\"10\" />\n    <circle cx=\"92\" cy=\"196\" r=\"10\" />\n    <circle cx=\"164\" cy=\"196\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", () => (\n  <>\n    <circle cx=\"92\" cy=\"60\" r=\"8\" />\n    <circle cx=\"164\" cy=\"60\" r=\"8\" />\n    <circle cx=\"92\" cy=\"128\" r=\"8\" />\n    <circle cx=\"164\" cy=\"128\" r=\"8\" />\n    <circle cx=\"92\" cy=\"196\" r=\"8\" />\n    <circle cx=\"164\" cy=\"196\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", () => (\n  <>\n    <circle cx=\"92\" cy=\"60\" r=\"12\" />\n    <circle cx=\"164\" cy=\"60\" r=\"12\" />\n    <circle cx=\"92\" cy=\"128\" r=\"12\" />\n    <circle cx=\"164\" cy=\"128\" r=\"12\" />\n    <circle cx=\"92\" cy=\"196\" r=\"12\" />\n    <circle cx=\"164\" cy=\"196\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst DotsSixVertical = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nDotsSixVertical.displayName = \"DotsSixVertical\";\n\nexport default DotsSixVertical;\n"], "names": ["pathsByWeight", "Map", "set", "React", "cx", "cy", "r", "d", "<PERSON><PERSON><PERSON>", "weight", "color", "renderPathForWeight", "DotsSixVertical", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,CADwB;AAAA,CAA1B;AAWAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,CAD2B;AAAA,CAA7B;AAWAN,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMI,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAP,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,CADyB;AAAA,CAA3B;AAWAN,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,CADwB;AAAA,CAA1B;AAWAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,CAD2B;AAAA,CAA7B;;AAWA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBC,KAArB;AAAA,SACjCC,mBAAmB,CAACF,MAAD,EAASC,KAAT,EAAgBV,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMY,eAAe,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAC3DZ,mBAAA,CAACa,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAON,IAAAA,UAAU,EAAEA;IAA3C,CAD2D;AAAA,CAA3B,CAAlC;AAIAI,eAAe,CAACK,WAAhB,GAA8B,iBAA9B;;;;"}