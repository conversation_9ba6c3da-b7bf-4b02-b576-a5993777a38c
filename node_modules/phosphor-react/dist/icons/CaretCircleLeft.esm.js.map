{"version": 3, "file": "CaretCircleLeft.esm.js", "sources": ["../../src/icons/CaretCircleLeft.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeMiterlimit=\"10\"\n      strokeWidth=\"24\"\n    />\n    <polyline\n      points=\"144 92 104 128 144 164\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <circle cx=\"128\" cy=\"128\" r=\"96\" opacity=\"0.2\" />\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeMiterlimit=\"10\"\n      strokeWidth=\"16\"\n    />\n    <polyline\n      points=\"144 92 104 128 144 164\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M128,24A104,104,0,1,0,232,128,104.2,104.2,0,0,0,128,24Zm21.4,134.1a8,8,0,0,1,.5,11.3A7.9,7.9,0,0,1,144,172a8.2,8.2,0,0,1-5.4-2.1l-40-36a8,8,0,0,1,0-11.8l40-36a8,8,0,1,1,10.8,11.8L116,128Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeMiterlimit=\"10\"\n      strokeWidth=\"12\"\n    />\n    <polyline\n      points=\"144 92 104 128 144 164\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeMiterlimit=\"10\"\n      strokeWidth=\"8\"\n    />\n    <polyline\n      points=\"144 92 104 128 144 164\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <circle\n      cx=\"128\"\n      cy=\"128\"\n      r=\"96\"\n      fill=\"none\"\n      stroke={color}\n      strokeMiterlimit=\"10\"\n      strokeWidth=\"16\"\n    />\n    <polyline\n      points=\"144 92 104 128 144 164\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst CaretCircleLeft = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nCaretCircleLeft.displayName = \"CaretCircleLeft\";\n\nexport default CaretCircleLeft;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "cx", "cy", "r", "fill", "stroke", "strokeMiterlimit", "strokeWidth", "points", "strokeLinecap", "strokeLinejoin", "opacity", "d", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "CaretCircleLeft", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,gBAAgB,EAAC;AACjBC,IAAAA,WAAW,EAAC;GAPd,CADF,EAUEP,mBAAA,WAAA;AACEQ,IAAAA,MAAM,EAAC;AACPJ,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfH,IAAAA,WAAW,EAAC;GANd,CAVF,CADwB;AAAA,CAA1B;AAsBAX,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;AAAKQ,IAAAA,OAAO,EAAC;GAAzC,CADF,EAEEX,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,gBAAgB,EAAC;AACjBC,IAAAA,WAAW,EAAC;GAPd,CAFF,EAWEP,mBAAA,WAAA;AACEQ,IAAAA,MAAM,EAAC;AACPJ,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfH,IAAAA,WAAW,EAAC;GANd,CAXF,CAD2B;AAAA,CAA7B;AAuBAX,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMY,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAhB,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,gBAAgB,EAAC;AACjBC,IAAAA,WAAW,EAAC;GAPd,CADF,EAUEP,mBAAA,WAAA;AACEQ,IAAAA,MAAM,EAAC;AACPJ,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfH,IAAAA,WAAW,EAAC;GANd,CAVF,CADyB;AAAA,CAA3B;AAsBAX,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,gBAAgB,EAAC;AACjBC,IAAAA,WAAW,EAAC;GAPd,CADF,EAUEP,mBAAA,WAAA;AACEQ,IAAAA,MAAM,EAAC;AACPJ,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfH,IAAAA,WAAW,EAAC;GANd,CAVF,CADwB;AAAA,CAA1B;AAsBAX,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRO,IAAAA,gBAAgB,EAAC;AACjBC,IAAAA,WAAW,EAAC;GAPd,CADF,EAUEP,mBAAA,WAAA;AACEQ,IAAAA,MAAM,EAAC;AACPJ,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEN;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfH,IAAAA,WAAW,EAAC;GANd,CAVF,CAD2B;AAAA,CAA7B;;AAsBA,IAAMM,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBf,KAArB;AAAA,SACjCgB,mBAAmB,CAACD,MAAD,EAASf,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMoB,eAAe,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAC3DnB,mBAAA,CAACoB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CAD2D;AAAA,CAA3B,CAAlC;AAIAG,eAAe,CAACK,WAAhB,GAA8B,iBAA9B;;;;"}