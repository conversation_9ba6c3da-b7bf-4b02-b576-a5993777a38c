{"version": 3, "file": "ArchiveTray.esm.js", "sources": ["../../src/icons/ArchiveTray.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <rect\n      x=\"40\"\n      y=\"40\"\n      width=\"176\"\n      height=\"176\"\n      rx=\"8\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <polyline\n      points=\"99.7 115.7 128 144 156.3 115.7\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"76\"\n      x2=\"128\"\n      y2=\"144\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <path\n      d=\"M40,156H76.7a7.9,7.9,0,0,1,5.6,2.3l19.4,19.4a7.9,7.9,0,0,0,5.6,2.3h41.4a7.9,7.9,0,0,0,5.6-2.3l19.4-19.4a7.9,7.9,0,0,1,5.6-2.3H216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M76.7,160a7.9,7.9,0,0,1,5.6,2.3l19.4,19.4a7.9,7.9,0,0,0,5.6,2.3h41.4a7.9,7.9,0,0,0,5.6-2.3l19.4-19.4a7.9,7.9,0,0,1,5.6-2.3H216V48a8,8,0,0,0-8-8H48a8,8,0,0,0-8,8V160Z\"\n      opacity=\"0.2\"\n    />\n    <rect\n      x=\"40\"\n      y=\"40\"\n      width=\"176\"\n      height=\"176\"\n      rx=\"8\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <polyline\n      points=\"94.1 118.1 128 152 161.9 118.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"72\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M40,160H76.7a7.9,7.9,0,0,1,5.6,2.3l19.4,19.4a7.9,7.9,0,0,0,5.6,2.3h41.4a7.9,7.9,0,0,0,5.6-2.3l19.4-19.4a7.9,7.9,0,0,1,5.6-2.3H216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M208,32H48A16,16,0,0,0,32,48V159.9h0V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM88.4,112.4a8,8,0,0,1,11.3,0L120,132.7V72a8,8,0,0,1,16,0v60.7l20.3-20.3a8,8,0,0,1,11.3,11.3l-33.9,34h-.2l-.4.4h-.2l-.5.3c0,.1-.1.1-.1.2l-.5.3h-.2l-.5.3H131l-.7.3h-4.6l-.7-.3h-.1l-.5-.3h-.2l-.5-.3c0-.1-.1-.1-.1-.2l-.5-.3h-.2l-.4-.4h-.2l-33.9-34A8,8,0,0,1,88.4,112.4ZM208,208H48V168H76.7L96,187.3a15.9,15.9,0,0,0,11.3,4.7h41.4a15.9,15.9,0,0,0,11.3-4.7L179.3,168H208v40Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <rect\n      x=\"40\"\n      y=\"40\"\n      width=\"176\"\n      height=\"176\"\n      rx=\"8\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <polyline\n      points=\"94.1 118.1 128 152 161.9 118.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"72\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <path\n      d=\"M40,160H76.7a7.9,7.9,0,0,1,5.6,2.3l19.4,19.4a7.9,7.9,0,0,0,5.6,2.3h41.4a7.9,7.9,0,0,0,5.6-2.3l19.4-19.4a7.9,7.9,0,0,1,5.6-2.3H216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <rect\n      x=\"40\"\n      y=\"40\"\n      width=\"176\"\n      height=\"176\"\n      rx=\"8\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <polyline\n      points=\"94.1 118.1 128 152 161.9 118.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"72\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <path\n      d=\"M40,160H76.7a7.9,7.9,0,0,1,5.6,2.3l19.4,19.4a7.9,7.9,0,0,0,5.6,2.3h41.4a7.9,7.9,0,0,0,5.6-2.3l19.4-19.4a7.9,7.9,0,0,1,5.6-2.3H216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <rect\n      x=\"40\"\n      y=\"40\"\n      width=\"176\"\n      height=\"176\"\n      rx=\"8\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <polyline\n      points=\"94.1 118.1 128 152 161.9 118.1\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"128\"\n      y1=\"72\"\n      x2=\"128\"\n      y2=\"152\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <path\n      d=\"M40,160H76.7a7.9,7.9,0,0,1,5.6,2.3l19.4,19.4a7.9,7.9,0,0,0,5.6,2.3h41.4a7.9,7.9,0,0,0,5.6-2.3l19.4-19.4a7.9,7.9,0,0,1,5.6-2.3H216\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst ArchiveTray = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nArchiveTray.displayName = \"ArchiveTray\";\n\nexport default ArchiveTray;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "x", "y", "width", "height", "rx", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "points", "x1", "y1", "x2", "y2", "d", "opacity", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "ArchiveTray", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,WAAA;AACEW,IAAAA,MAAM,EAAC;AACPL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CArBF,EAgCEV,mBAAA,OAAA;AACEgB,IAAAA,CAAC,EAAC;AACFV,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAhCF,CADwB;AAAA,CAA1B;AA4CAd,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEgB,IAAAA,CAAC,EAAC;AACFC,IAAAA,OAAO,EAAC;GAFV,CADF,EAKEjB,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CALF,EAiBEV,mBAAA,WAAA;AACEW,IAAAA,MAAM,EAAC;AACPL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAjBF,EAyBEV,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAzBF,EAoCEV,mBAAA,OAAA;AACEgB,IAAAA,CAAC,EAAC;AACFV,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CApCF,CAD2B;AAAA,CAA7B;AAgDAd,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMgB,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMApB,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,WAAA;AACEW,IAAAA,MAAM,EAAC;AACPL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CArBF,EAgCEV,mBAAA,OAAA;AACEgB,IAAAA,CAAC,EAAC;AACFV,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAhCF,CADyB;AAAA,CAA3B;AA4CAd,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,WAAA;AACEW,IAAAA,MAAM,EAAC;AACPL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CArBF,EAgCEV,mBAAA,OAAA;AACEgB,IAAAA,CAAC,EAAC;AACFV,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAhCF,CADwB;AAAA,CAA1B;AA4CAd,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,WAAA;AACEW,IAAAA,MAAM,EAAC;AACPL,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAbF,EAqBEV,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHT,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CArBF,EAgCEV,mBAAA,OAAA;AACEgB,IAAAA,CAAC,EAAC;AACFV,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAhCF,CAD2B;AAAA,CAA7B;;AA4CA,IAAMQ,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBpB,KAArB;AAAA,SACjCqB,mBAAmB,CAACD,MAAD,EAASpB,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMyB,WAAW,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACvDxB,mBAAA,CAACyB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADuD;AAAA,CAA3B,CAA9B;AAIAG,WAAW,CAACK,WAAZ,GAA0B,aAA1B;;;;"}