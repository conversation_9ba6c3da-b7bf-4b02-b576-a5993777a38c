{"version": 3, "file": "BugDroid.esm.js", "sources": ["../../src/icons/BugDroid.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <circle cx=\"156\" cy=\"84\" r=\"16\" />\n    <circle cx=\"100\" cy=\"84\" r=\"16\" />\n    <path\n      d=\"M207.9,148a80,80,0,0,1-160,0V108a80,80,0,0,1,160,0Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"207.9\"\n      y1=\"128\"\n      x2=\"47.9\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"55.9\"\n      y1=\"28\"\n      x2=\"76.2\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"199.9\"\n      y1=\"28\"\n      x2=\"179.6\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path d=\"M47.9,128h160v20a80,80,0,0,1-160,0Z\" opacity=\"0.2\" />\n    <path\n      d=\"M207.9,148a80,80,0,0,1-160,0V108a80,80,0,0,1,160,0Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"207.9\"\n      y1=\"128\"\n      x2=\"47.9\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"155.9\" cy=\"92\" r=\"12\" />\n    <circle cx=\"99.9\" cy=\"92\" r=\"12\" />\n    <line\n      x1=\"55.9\"\n      y1=\"28\"\n      x2=\"76.2\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"199.9\"\n      y1=\"28\"\n      x2=\"179.6\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M191.3,47l14.1-13.1a8.2,8.2,0,0,0,.4-11.4,8,8,0,0,0-11.3-.4L179.1,36.5a87.7,87.7,0,0,0-102.4,0L61.4,22.1A8,8,0,0,0,50.5,33.9L64.6,47a87.4,87.4,0,0,0-24.7,61v40a88,88,0,0,0,176,0V108A87.7,87.7,0,0,0,191.3,47ZM127.9,36a72.1,72.1,0,0,1,72,72v12H55.9V108A72.1,72.1,0,0,1,127.9,36Zm16,56a12,12,0,1,1,12,12A12,12,0,0,1,143.9,92Zm-56,0a12,12,0,1,1,12,12A12,12,0,0,1,87.9,92Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <circle cx=\"156\" cy=\"84\" r=\"10\" />\n    <circle cx=\"100\" cy=\"84\" r=\"10\" />\n    <path\n      d=\"M207.9,148a80,80,0,0,1-160,0V108a80,80,0,0,1,160,0Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"207.9\"\n      y1=\"128\"\n      x2=\"47.9\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"55.9\"\n      y1=\"28\"\n      x2=\"76.2\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"199.9\"\n      y1=\"28\"\n      x2=\"179.6\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <circle cx=\"156\" cy=\"84\" r=\"8\" />\n    <circle cx=\"100\" cy=\"84\" r=\"8\" />\n    <path\n      d=\"M207.9,148a80,80,0,0,1-160,0V108a80,80,0,0,1,160,0Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"207.9\"\n      y1=\"128\"\n      x2=\"47.9\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"55.9\"\n      y1=\"28\"\n      x2=\"76.2\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"199.9\"\n      y1=\"28\"\n      x2=\"179.6\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <path\n      d=\"M208,148a80,80,0,0,1-160,0V108a80,80,0,0,1,160,0Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"208\"\n      y1=\"128\"\n      x2=\"48\"\n      y2=\"128\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"156\" cy=\"92\" r=\"12\" />\n    <circle cx=\"100\" cy=\"92\" r=\"12\" />\n    <line\n      x1=\"56\"\n      y1=\"28\"\n      x2=\"76.3\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"200\"\n      y1=\"28\"\n      x2=\"179.7\"\n      y2=\"46.9\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst BugDroid = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nBugDroid.displayName = \"BugDroid\";\n\nexport default BugDroid;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "cx", "cy", "r", "d", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "x1", "y1", "x2", "y2", "opacity", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "BugDroid", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAHF,EAWET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAXF,EAsBET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAtBF,EAiCET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAjCF,CADwB;AAAA,CAA1B;AAgDAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMI,IAAAA,CAAC,EAAC;AAAsCU,IAAAA,OAAO,EAAC;GAAtD,CADF,EAEEd,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAFF,EAUET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAVF,EAqBET,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA7B,CArBF,EAsBEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAOC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA5B,CAtBF,EAuBEH,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAvBF,EAkCET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAlCF,CAD2B;AAAA,CAA7B;AAiDAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMI,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAR,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAHF,EAWET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAXF,EAsBET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAtBF,EAiCET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAjCF,CADyB;AAAA,CAA3B;AAgDAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CAHF,EAWET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAXF,EAsBET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAtBF,EAiCET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAjCF,CADwB;AAAA,CAA1B;AAgDAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEI,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CATF,EAoBET,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CApBF,EAqBEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CArBF,EAsBEH,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAtBF,EAiCET,mBAAA,OAAA;AACEU,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEP;AACRQ,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAjCF,CAD2B;AAAA,CAA7B;;AAgDA,IAAMM,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBjB,KAArB;AAAA,SACjCkB,mBAAmB,CAACD,MAAD,EAASjB,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMsB,QAAQ,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACpDrB,mBAAA,CAACsB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADoD;AAAA,CAA3B,CAA3B;AAIAG,QAAQ,CAACK,WAAT,GAAuB,UAAvB;;;;"}