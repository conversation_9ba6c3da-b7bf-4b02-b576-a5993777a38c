{"version": 3, "file": "DotsNine.esm.js", "sources": ["../../src/icons/DotsNine.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", () => (\n  <>\n    <circle cx=\"60\" cy=\"60\" r=\"16\" />\n    <circle cx=\"128\" cy=\"60\" r=\"16\" />\n    <circle cx=\"196\" cy=\"60\" r=\"16\" />\n    <circle cx=\"60\" cy=\"128\" r=\"16\" />\n    <circle cx=\"128\" cy=\"128\" r=\"16\" />\n    <circle cx=\"196\" cy=\"128\" r=\"16\" />\n    <circle cx=\"60\" cy=\"196\" r=\"16\" />\n    <circle cx=\"128\" cy=\"196\" r=\"16\" />\n    <circle cx=\"196\" cy=\"196\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", () => (\n  <>\n    <circle cx=\"60\" cy=\"60\" r=\"12\" />\n    <circle cx=\"128\" cy=\"60\" r=\"12\" />\n    <circle cx=\"196\" cy=\"60\" r=\"12\" />\n    <circle cx=\"60\" cy=\"128\" r=\"12\" />\n    <circle cx=\"128\" cy=\"128\" r=\"12\" />\n    <circle cx=\"196\" cy=\"128\" r=\"12\" />\n    <circle cx=\"60\" cy=\"196\" r=\"12\" />\n    <circle cx=\"128\" cy=\"196\" r=\"12\" />\n    <circle cx=\"196\" cy=\"196\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M60,48A12,12,0,1,0,72,60,12,12,0,0,0,60,48Zm68,0a12,12,0,1,0,12,12A12,12,0,0,0,128,48Zm68,24a12,12,0,1,0-12-12A12,12,0,0,0,196,72ZM60,184a12,12,0,1,0,12,12A12,12,0,0,0,60,184Zm68,0a12,12,0,1,0,12,12A12,12,0,0,0,128,184Zm68,0a12,12,0,1,0,12,12A12,12,0,0,0,196,184ZM60,116a12,12,0,1,0,12,12A12,12,0,0,0,60,116Zm68,0a12,12,0,1,0,12,12A12,12,0,0,0,128,116Zm68,0a12,12,0,1,0,12,12A12,12,0,0,0,196,116Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", () => (\n  <>\n    <circle cx=\"60\" cy=\"60\" r=\"10\" />\n    <circle cx=\"128\" cy=\"60\" r=\"10\" />\n    <circle cx=\"196\" cy=\"60\" r=\"10\" />\n    <circle cx=\"60\" cy=\"128\" r=\"10\" />\n    <circle cx=\"128\" cy=\"128\" r=\"10\" />\n    <circle cx=\"196\" cy=\"128\" r=\"10\" />\n    <circle cx=\"60\" cy=\"196\" r=\"10\" />\n    <circle cx=\"128\" cy=\"196\" r=\"10\" />\n    <circle cx=\"196\" cy=\"196\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", () => (\n  <>\n    <circle cx=\"60\" cy=\"60\" r=\"8\" />\n    <circle cx=\"128\" cy=\"60\" r=\"8\" />\n    <circle cx=\"196\" cy=\"60\" r=\"8\" />\n    <circle cx=\"60\" cy=\"128\" r=\"8\" />\n    <circle cx=\"128\" cy=\"128\" r=\"8\" />\n    <circle cx=\"196\" cy=\"128\" r=\"8\" />\n    <circle cx=\"60\" cy=\"196\" r=\"8\" />\n    <circle cx=\"128\" cy=\"196\" r=\"8\" />\n    <circle cx=\"196\" cy=\"196\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", () => (\n  <>\n    <circle cx=\"60\" cy=\"60\" r=\"12\" />\n    <circle cx=\"128\" cy=\"60\" r=\"12\" />\n    <circle cx=\"196\" cy=\"60\" r=\"12\" />\n    <circle cx=\"60\" cy=\"128\" r=\"12\" />\n    <circle cx=\"128\" cy=\"128\" r=\"12\" />\n    <circle cx=\"196\" cy=\"128\" r=\"12\" />\n    <circle cx=\"60\" cy=\"196\" r=\"12\" />\n    <circle cx=\"128\" cy=\"196\" r=\"12\" />\n    <circle cx=\"196\" cy=\"196\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst DotsNine = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nDotsNine.displayName = \"DotsNine\";\n\nexport default DotsNine;\n"], "names": ["pathsByWeight", "Map", "set", "React", "cx", "cy", "r", "d", "<PERSON><PERSON><PERSON>", "weight", "color", "renderPathForWeight", "DotsNine", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,EAOEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAPF,EAQEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CARF,EASEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,CADwB;AAAA,CAA1B;AAcAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,EAOEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAPF,EAQEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CARF,EASEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,CAD2B;AAAA,CAA7B;AAcAN,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMI,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAP,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,EAOEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAPF,EAQEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CARF,EASEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,CADyB;AAAA,CAA3B;AAcAN,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,EAOEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAPF,EAQEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CARF,EASEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,CADwB;AAAA,CAA1B;AAcAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA1B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,CAAC,EAAC;GAA3B,CAHF,EAIEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAJF,EAKEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CALF,EAMEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CANF,EAOEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAPF,EAQEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CARF,EASEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,CAD2B;AAAA,CAA7B;;AAcA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBC,KAArB;AAAA,SACjCC,mBAAmB,CAACF,MAAD,EAASC,KAAT,EAAgBV,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMY,QAAQ,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACpDZ,mBAAA,CAACa,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAON,IAAAA,UAAU,EAAEA;IAA3C,CADoD;AAAA,CAA3B,CAA3B;AAIAI,QAAQ,CAACK,WAAT,GAAuB,UAAvB;;;;"}