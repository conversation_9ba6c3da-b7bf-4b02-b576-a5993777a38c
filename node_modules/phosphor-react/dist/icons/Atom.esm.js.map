{"version": 3, "file": "Atom.esm.js", "sources": ["../../src/icons/Atom.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"44\"\n      ry=\"116\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"116\"\n      ry=\"44\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <circle cx=\"128\" cy=\"128\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"44\"\n      ry=\"116\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      opacity=\"0.2\"\n    />\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"44\"\n      ry=\"116\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"116\"\n      ry=\"44\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"128\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M196.1,128a195.3,195.3,0,0,0,19.2-32.7c11.1-24.6,11.2-44.2.4-55C198.2,22.9,162.5,35.4,128,60,93.5,35.4,57.8,22.9,40.3,40.3c-10.8,10.8-10.7,30.4.4,55A195.3,195.3,0,0,0,59.9,128a195.3,195.3,0,0,0-19.2,32.7c-11.1,24.6-11.2,44.2-.4,55,5.6,5.6,13.1,8.1,21.9,8.1,18.4,0,42.4-11.1,65.8-27.8,23.4,16.7,47.4,27.8,65.8,27.8,8.8,0,16.3-2.5,21.9-8.1,10.8-10.8,10.7-30.4-.4-55A195.3,195.3,0,0,0,196.1,128Zm-2.3-79.7c4.8,0,8.4,1.1,10.6,3.3,5.6,5.7,4.2,19.5-3.7,37.1a169.7,169.7,0,0,1-14.8,25.9c-6.4-7.9-13.5-15.7-21.1-23.4s-15.4-14.5-23.5-21.1C163.2,55.2,182,48.3,193.8,48.3ZM176.1,128a277.3,277.3,0,0,1-22.6,25.5A314.1,314.1,0,0,1,128,176.2a314.1,314.1,0,0,1-25.5-22.7A277.3,277.3,0,0,1,79.9,128a277.3,277.3,0,0,1,22.6-25.5A314.1,314.1,0,0,1,128,79.8a314.1,314.1,0,0,1,25.5,22.7A277.3,277.3,0,0,1,176.1,128ZM55.3,88.7C47.4,71.1,46,57.3,51.6,51.6c2.2-2.2,5.8-3.3,10.6-3.3,11.8,0,30.6,6.9,52.5,21.8-8.1,6.6-16,13.7-23.5,21.1s-14.7,15.5-21.1,23.4A169.7,169.7,0,0,1,55.3,88.7ZM51.6,204.4c-5.6-5.7-4.2-19.5,3.7-37.1a169.7,169.7,0,0,1,14.8-25.9c6.4,7.9,13.5,15.7,21.1,23.4s15.4,14.5,23.5,21.1C83.9,206.8,59.2,212,51.6,204.4Zm152.8,0c-2.2,2.2-5.8,3.3-10.6,3.3-11.8,0-30.6-6.9-52.5-21.8,8.1-6.6,16-13.7,23.5-21.1s14.7-15.5,21.1-23.4a169.7,169.7,0,0,1,14.8,25.9C208.6,184.9,210,198.7,204.4,204.4ZM140,128a12,12,0,1,1-12-12A12,12,0,0,1,140,128Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"44\"\n      ry=\"116\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"116\"\n      ry=\"44\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <circle cx=\"128\" cy=\"128\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"44\"\n      ry=\"116\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"116\"\n      ry=\"44\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <circle cx=\"128\" cy=\"128\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"44\"\n      ry=\"116\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <ellipse\n      cx=\"128\"\n      cy=\"128\"\n      rx=\"116\"\n      ry=\"44\"\n      transform=\"translate(-53 128) rotate(-45)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"128\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst Atom = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nAtom.displayName = \"Atom\";\n\nexport default Atom;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "cx", "cy", "rx", "ry", "transform", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "r", "opacity", "d", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "Atom", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CAbF,EAyBEV,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMS,IAAAA,CAAC,EAAC;GAA5B,CAzBF,CADwB;AAAA,CAA1B;AA8BAf,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVO,IAAAA,OAAO,EAAC;GANV,CADF,EASEZ,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CATF,EAqBEV,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CArBF,EAiCEV,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMS,IAAAA,CAAC,EAAC;GAA5B,CAjCF,CAD2B;AAAA,CAA7B;AAsCAf,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMa,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAjB,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CAbF,EAyBEV,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMS,IAAAA,CAAC,EAAC;GAA5B,CAzBF,CADyB;AAAA,CAA3B;AA8BAf,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CAbF,EAyBEV,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMS,IAAAA,CAAC,EAAC;GAA5B,CAzBF,CADwB;AAAA,CAA1B;AA8BAf,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CADF,EAaEV,mBAAA,UAAA;AACEC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAER;AACRS,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAVd,CAbF,EAyBEV,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMS,IAAAA,CAAC,EAAC;GAA5B,CAzBF,CAD2B;AAAA,CAA7B;;AA8BA,IAAMG,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBhB,KAArB;AAAA,SACjCiB,mBAAmB,CAACD,MAAD,EAAShB,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMqB,IAAI,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAChDpB,mBAAA,CAACqB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADgD;AAAA,CAA3B,CAAvB;AAIAG,IAAI,CAACK,WAAL,GAAmB,MAAnB;;;;"}