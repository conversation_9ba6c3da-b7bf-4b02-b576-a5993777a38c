{"version": 3, "file": "ChatCenteredDots.esm.js", "sources": ["../../src/icons/ChatCenteredDots.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <path\n      d=\"M149.7,195.9l-14.8,24.7a8.1,8.1,0,0,1-13.8,0l-14.8-24.7a7.9,7.9,0,0,0-6.8-3.9H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216a8,8,0,0,1,8,8V184a8,8,0,0,1-8,8H156.5A7.9,7.9,0,0,0,149.7,195.9Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <circle cx=\"100\" cy=\"120\" r=\"16\" />\n    <circle cx=\"156\" cy=\"120\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M149.7,195.9l-14.8,24.7a8.1,8.1,0,0,1-13.8,0l-14.8-24.7a7.9,7.9,0,0,0-6.8-3.9H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216a8,8,0,0,1,8,8V184a8,8,0,0,1-8,8H156.5A7.9,7.9,0,0,0,149.7,195.9Z\"\n      opacity=\"0.2\"\n    />\n    <path\n      d=\"M149.7,195.9l-14.8,24.7a8.1,8.1,0,0,1-13.8,0l-14.8-24.7a7.9,7.9,0,0,0-6.8-3.9H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216a8,8,0,0,1,8,8V184a8,8,0,0,1-8,8H156.5A7.9,7.9,0,0,0,149.7,195.9Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"120\" r=\"12\" />\n    <circle cx=\"80\" cy=\"120\" r=\"12\" />\n    <circle cx=\"176\" cy=\"120\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M216,40H40A16,16,0,0,0,24,56V184a16,16,0,0,0,16,16H99.5l14.8,24.7a16,16,0,0,0,27.4,0L156.5,200H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40ZM80,132a12,12,0,1,1,12-12A12,12,0,0,1,80,132Zm48,0a12,12,0,1,1,12-12A12,12,0,0,1,128,132Zm48,0a12,12,0,1,1,12-12A12,12,0,0,1,176,132Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <path\n      d=\"M149.7,195.9l-14.8,24.7a8.1,8.1,0,0,1-13.8,0l-14.8-24.7a7.9,7.9,0,0,0-6.8-3.9H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216a8,8,0,0,1,8,8V184a8,8,0,0,1-8,8H156.5A7.9,7.9,0,0,0,149.7,195.9Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <circle cx=\"128\" cy=\"120\" r=\"10\" />\n    <circle cx=\"176\" cy=\"120\" r=\"10\" />\n    <circle cx=\"80\" cy=\"120\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <path\n      d=\"M149.7,195.9l-14.8,24.7a8.1,8.1,0,0,1-13.8,0l-14.8-24.7a7.9,7.9,0,0,0-6.8-3.9H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216a8,8,0,0,1,8,8V184a8,8,0,0,1-8,8H156.5A7.9,7.9,0,0,0,149.7,195.9Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <circle cx=\"128\" cy=\"120\" r=\"8\" />\n    <circle cx=\"80\" cy=\"120\" r=\"8\" />\n    <circle cx=\"176\" cy=\"120\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <path\n      d=\"M149.7,195.9l-14.8,24.7a8.1,8.1,0,0,1-13.8,0l-14.8-24.7a7.9,7.9,0,0,0-6.8-3.9H40a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8H216a8,8,0,0,1,8,8V184a8,8,0,0,1-8,8H156.5A7.9,7.9,0,0,0,149.7,195.9Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <circle cx=\"128\" cy=\"120\" r=\"12\" />\n    <circle cx=\"80\" cy=\"120\" r=\"12\" />\n    <circle cx=\"176\" cy=\"120\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst ChatCenteredDots = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nChatCenteredDots.displayName = \"ChatCenteredDots\";\n\nexport default ChatCenteredDots;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "d", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "cx", "cy", "r", "opacity", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "ChatCenteredDots", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,EAUET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAVF,CADwB;AAAA,CAA1B;AAeAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFS,IAAAA,OAAO,EAAC;GAFV,CADF,EAKEV,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CALF,EAaEN,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAbF,EAcET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAdF,EAeET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAfF,CAD2B;AAAA,CAA7B;AAoBAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMC,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAL,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,EAUET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAVF,EAWET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAXF,CADyB;AAAA,CAA3B;AAgBAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,EAUET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAVF,EAWET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAXF,CADwB;AAAA,CAA1B;AAgBAb,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEJ;AACRK,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,EASEN,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CATF,EAUET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAVF,EAWET,mBAAA,SAAA;AAAQO,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAXF,CAD2B;AAAA,CAA7B;;AAgBA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBb,KAArB;AAAA,SACjCc,mBAAmB,CAACD,MAAD,EAASb,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMkB,gBAAgB,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SAC5DjB,mBAAA,CAACkB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CAD4D;AAAA,CAA3B,CAAnC;AAIAG,gBAAgB,CAACK,WAAjB,GAA+B,kBAA/B;;;;"}