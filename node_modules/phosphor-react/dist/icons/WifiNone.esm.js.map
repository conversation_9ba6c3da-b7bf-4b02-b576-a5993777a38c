{"version": 3, "file": "WifiNone.esm.js", "sources": ["../../src/icons/WifiNone.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", () => (\n  <>\n    <circle cx=\"128\" cy=\"200\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <path\n      d=\"M20.2,75.9C83.7,28,172.3,28,235.8,75.9A8.1,8.1,0,0,1,237,87.5c-19,22.5-83,97.8-103,121.4a7.9,7.9,0,0,1-12,0C102,185.3,38,110,19,87.5A8.1,8.1,0,0,1,20.2,75.9Z\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M128,219.7a16,16,0,0,1-12.1-5.6L12.9,92.7A15.8,15.8,0,0,1,9.2,80.5a16.3,16.3,0,0,1,6.2-11c66.3-50,158.9-50,225.2,0a16.3,16.3,0,0,1,6.2,11,16,16,0,0,1-3.7,12.2l-103,121.4A16,16,0,0,1,128,219.7ZM25.1,82.3h0L128,203.6,230.9,82.4v-.2C170.4,36.6,85.7,36.6,25.1,82.3Zm-.1,0Zm-4.8-6.4Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", () => (\n  <>\n    <circle cx=\"128\" cy=\"200\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", () => (\n  <>\n    <circle cx=\"128\" cy=\"200\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", () => (\n  <>\n    <circle cx=\"128\" cy=\"200\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst WifiNone = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nWifiNone.displayName = \"WifiNone\";\n\nexport default WifiNone;\n"], "names": ["pathsByWeight", "Map", "set", "React", "cx", "cy", "r", "color", "d", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "WifiNone", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,CADwB;AAAA,CAA1B;AAMAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACK,KAAD;AAAA,SAC3BJ,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEK,IAAAA,CAAC,EAAC;AACFC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAEH;AACRI,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GANd,CADF,CAD2B;AAAA,CAA7B;AAaAb,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMK,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAR,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,CADyB;AAAA,CAA3B;AAMAN,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,CADwB;AAAA,CAA1B;AAMAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,CAD2B;AAAA,CAA7B;;AAMA,IAAMQ,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBR,KAArB;AAAA,SACjCS,mBAAmB,CAACD,MAAD,EAASR,KAAT,EAAgBP,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMiB,QAAQ,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACpDjB,mBAAA,CAACkB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADoD;AAAA,CAA3B,CAA3B;AAIAG,QAAQ,CAACK,WAAT,GAAuB,UAAvB;;;;"}