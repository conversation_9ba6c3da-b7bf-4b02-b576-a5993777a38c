import React, { forwardRef } from 'react';
import { renderPathForWeight } from '../lib/index.esm.js';
import IconBase from '../lib/IconBase.esm.js';

/* GENERATED FILE */
var pathsByWeight = /*#__PURE__*/new Map();
pathsByWeight.set("bold", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("circle", {
    cx: "128",
    cy: "128",
    r: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "96",
    x2: "218.5",
    y2: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "160",
    x2: "218.5",
    y2: "160",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }), React.createElement("ellipse", {
    cx: "128",
    cy: "128",
    rx: "40",
    ry: "93.4",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }));
});
pathsByWeight.set("duotone", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("circle", {
    cx: "128",
    cy: "128",
    r: "96",
    opacity: "0.2"
  }), React.createElement("circle", {
    cx: "128",
    cy: "128",
    r: "96",
    fill: "none",
    stroke: color,
    strokeMiterlimit: "10",
    strokeWidth: "16"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "96",
    x2: "218.5",
    y2: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "160",
    x2: "218.5",
    y2: "160",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("ellipse", {
    cx: "128",
    cy: "128",
    rx: "40",
    ry: "93.4",
    fill: "none",
    stroke: color,
    strokeMiterlimit: "10",
    strokeWidth: "16"
  }));
});
pathsByWeight.set("fill", function () {
  return React.createElement(React.Fragment, null, React.createElement("path", {
    d: "M225.9,163.2l.3-1a103.7,103.7,0,0,0,0-68.4l-.3-1A104.2,104.2,0,0,0,128,24h0A104.2,104.2,0,0,0,30.1,92.8l-.3,1a103.7,103.7,0,0,0,0,68.4l.3,1A104.4,104.4,0,0,0,128,232h0A104.2,104.2,0,0,0,225.9,163.2ZM128,45.6A108.6,108.6,0,0,1,153.5,88h-51A108.6,108.6,0,0,1,128,45.6ZM102.5,168h51A108.6,108.6,0,0,1,128,210.4,108.6,108.6,0,0,1,102.5,168Zm-4.2-16a126.4,126.4,0,0,1,0-48h59.4a126.4,126.4,0,0,1,0,48ZM174,104h38.7a88.9,88.9,0,0,1,0,48H174a145,145,0,0,0,0-48Zm32.4-16h-36a128.7,128.7,0,0,0-24.1-46.1A88.6,88.6,0,0,1,206.4,88ZM146.3,214.1A128.7,128.7,0,0,0,170.4,168h36A88.6,88.6,0,0,1,146.3,214.1Z"
  }));
});
pathsByWeight.set("light", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("circle", {
    cx: "128",
    cy: "128",
    r: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "96",
    x2: "218.5",
    y2: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "160",
    x2: "218.5",
    y2: "160",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }), React.createElement("ellipse", {
    cx: "128",
    cy: "128",
    rx: "40",
    ry: "93.4",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }));
});
pathsByWeight.set("thin", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("circle", {
    cx: "128",
    cy: "128",
    r: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "96",
    x2: "218.5",
    y2: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "160",
    x2: "218.5",
    y2: "160",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }), React.createElement("ellipse", {
    cx: "128",
    cy: "128",
    rx: "40",
    ry: "93.4",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }));
});
pathsByWeight.set("regular", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("circle", {
    cx: "128",
    cy: "128",
    r: "96",
    fill: "none",
    stroke: color,
    strokeMiterlimit: "10",
    strokeWidth: "16"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "96",
    x2: "218.5",
    y2: "96",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("line", {
    x1: "37.5",
    y1: "160",
    x2: "218.5",
    y2: "160",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }), React.createElement("ellipse", {
    cx: "128",
    cy: "128",
    rx: "40",
    ry: "93.4",
    fill: "none",
    stroke: color,
    strokeMiterlimit: "10",
    strokeWidth: "16"
  }));
});

var renderPath = function renderPath(weight, color) {
  return renderPathForWeight(weight, color, pathsByWeight);
};

var Globe = /*#__PURE__*/forwardRef(function (props, ref) {
  return React.createElement(IconBase, Object.assign({
    ref: ref
  }, props, {
    renderPath: renderPath
  }));
});
Globe.displayName = "Globe";

export default Globe;
//# sourceMappingURL=Globe.esm.js.map
