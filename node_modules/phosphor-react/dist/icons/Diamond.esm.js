import React, { forwardRef } from 'react';
import { renderPathForWeight } from '../lib/index.esm.js';
import IconBase from '../lib/IconBase.esm.js';

/* GENERATED FILE */
var pathsByWeight = /*#__PURE__*/new Map();
pathsByWeight.set("bold", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "51.6",
    y: "51.6",
    width: "152.7",
    height: "152.74",
    rx: "8",
    transform: "translate(-53 128) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "24"
  }));
});
pathsByWeight.set("duotone", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "51.6",
    y: "51.6",
    width: "152.7",
    height: "152.74",
    rx: "8",
    transform: "translate(-53 128) rotate(-45)",
    opacity: "0.2"
  }), React.createElement("rect", {
    x: "51.6",
    y: "51.6",
    width: "152.7",
    height: "152.74",
    rx: "8",
    transform: "translate(-53 128) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }));
});
pathsByWeight.set("fill", function () {
  return React.createElement(React.Fragment, null, React.createElement("path", {
    d: "M236,139.3,139.3,236a15.9,15.9,0,0,1-22.6,0L20,139.3a16.1,16.1,0,0,1,0-22.6L116.7,20a16.1,16.1,0,0,1,22.6,0L236,116.7A16.1,16.1,0,0,1,236,139.3Z"
  }));
});
pathsByWeight.set("light", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "51.6",
    y: "51.6",
    width: "152.7",
    height: "152.74",
    rx: "8",
    transform: "translate(-53 128) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "12"
  }));
});
pathsByWeight.set("thin", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "51.6",
    y: "51.6",
    width: "152.7",
    height: "152.74",
    rx: "8",
    transform: "translate(-53 128) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "8"
  }));
});
pathsByWeight.set("regular", function (color) {
  return React.createElement(React.Fragment, null, React.createElement("rect", {
    x: "51.6",
    y: "51.6",
    width: "152.7",
    height: "152.74",
    rx: "8",
    transform: "translate(-53 128) rotate(-45)",
    fill: "none",
    stroke: color,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "16"
  }));
});

var renderPath = function renderPath(weight, color) {
  return renderPathForWeight(weight, color, pathsByWeight);
};

var Diamond = /*#__PURE__*/forwardRef(function (props, ref) {
  return React.createElement(IconBase, Object.assign({
    ref: ref
  }, props, {
    renderPath: renderPath
  }));
});
Diamond.displayName = "Diamond";

export default Diamond;
//# sourceMappingURL=Diamond.esm.js.map
