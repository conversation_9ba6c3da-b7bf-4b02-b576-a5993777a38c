{"version": 3, "file": "DotsThree.esm.js", "sources": ["../../src/icons/DotsThree.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", () => (\n  <>\n    <circle cx=\"128\" cy=\"128\" r=\"16\" />\n    <circle cx=\"64\" cy=\"128\" r=\"16\" />\n    <circle cx=\"192\" cy=\"128\" r=\"16\" />\n  </>\n));\n\npathsByWeight.set(\"duotone\", () => (\n  <>\n    <circle cx=\"128\" cy=\"128\" r=\"12\" />\n    <circle cx=\"192\" cy=\"128\" r=\"12\" />\n    <circle cx=\"64\" cy=\"128\" r=\"12\" />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M140,128a12,12,0,1,1-12-12A12,12,0,0,1,140,128Zm52-12a12,12,0,1,0,12,12A12,12,0,0,0,192,116ZM64,116a12,12,0,1,0,12,12A12,12,0,0,0,64,116Z\" />\n  </>\n));\n\npathsByWeight.set(\"light\", () => (\n  <>\n    <circle cx=\"128\" cy=\"128\" r=\"10\" />\n    <circle cx=\"64\" cy=\"128\" r=\"10\" />\n    <circle cx=\"192\" cy=\"128\" r=\"10\" />\n  </>\n));\n\npathsByWeight.set(\"thin\", () => (\n  <>\n    <circle cx=\"128\" cy=\"128\" r=\"8\" />\n    <circle cx=\"64\" cy=\"128\" r=\"8\" />\n    <circle cx=\"192\" cy=\"128\" r=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"regular\", () => (\n  <>\n    <circle cx=\"128\" cy=\"128\" r=\"12\" />\n    <circle cx=\"192\" cy=\"128\" r=\"12\" />\n    <circle cx=\"64\" cy=\"128\" r=\"12\" />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst DotsThree = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nDotsThree.displayName = \"DotsThree\";\n\nexport default DotsThree;\n"], "names": ["pathsByWeight", "Map", "set", "React", "cx", "cy", "r", "d", "<PERSON><PERSON><PERSON>", "weight", "color", "renderPathForWeight", "<PERSON>s<PERSON><PERSON><PERSON>", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAHF,CADwB;AAAA,CAA1B;AAQAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,CAD2B;AAAA,CAA7B;AAQAN,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMI,IAAAA,CAAC,EAAC;GAAR,CADF,CADwB;AAAA,CAA1B;AAMAP,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAHF,CADyB;AAAA,CAA3B;AAQAN,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAHF,CADwB;AAAA,CAA1B;AAQAN,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CADF,EAEEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA5B,CAFF,EAGEH,mBAAA,SAAA;AAAQC,IAAAA,EAAE,EAAC;AAAKC,IAAAA,EAAE,EAAC;AAAMC,IAAAA,CAAC,EAAC;GAA3B,CAHF,CAD2B;AAAA,CAA7B;;AAQA,IAAME,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBC,KAArB;AAAA,SACjCC,mBAAmB,CAACF,MAAD,EAASC,KAAT,EAAgBV,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMY,SAAS,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACrDZ,mBAAA,CAACa,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAON,IAAAA,UAAU,EAAEA;IAA3C,CADqD;AAAA,CAA3B,CAA5B;AAIAI,SAAS,CAACK,WAAV,GAAwB,WAAxB;;;;"}