{"version": 3, "file": "BatteryMedium.esm.js", "sources": ["../../src/icons/BatteryMedium.tsx"], "sourcesContent": ["/* GENERATED FILE */\nimport React, { forwardRef } from \"react\";\n\nimport {\n  IconWeight,\n  IconProps,\n  PaintFunction,\n  renderPathForWeight,\n} from \"../lib\";\nimport IconBase, { RenderFunction } from \"../lib/IconBase\";\n\nconst pathsByWeight = new Map<IconWeight, PaintFunction>();\n\npathsByWeight.set(\"bold\", (color: string) => (\n  <>\n    <rect\n      x=\"46\"\n      y=\"38\"\n      width=\"144\"\n      height=\"180\"\n      rx=\"16\"\n      transform=\"translate(246 10) rotate(90)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"64\"\n      y1=\"164\"\n      x2=\"64\"\n      y2=\"92\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"100\"\n      y1=\"164\"\n      x2=\"100\"\n      y2=\"92\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n    <line\n      x1=\"244\"\n      y1=\"96\"\n      x2=\"244\"\n      y2=\"160\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"24\"\n    />\n  </>\n));\n\npathsByWeight.set(\"duotone\", (color: string) => (\n  <>\n    <rect\n      x=\"52\"\n      y=\"36\"\n      width=\"144\"\n      height=\"184\"\n      rx=\"16\"\n      transform=\"translate(252 4) rotate(90)\"\n      opacity=\"0.2\"\n    />\n    <line\n      x1=\"248\"\n      y1=\"96\"\n      x2=\"248\"\n      y2=\"160\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <rect\n      x=\"52\"\n      y=\"36\"\n      width=\"144\"\n      height=\"184\"\n      rx=\"16\"\n      transform=\"translate(252 4) rotate(90)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"105.6\"\n      y1=\"168\"\n      x2=\"105.6\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"68.8\"\n      y1=\"168\"\n      x2=\"68.8\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\npathsByWeight.set(\"fill\", () => (\n  <>\n    <path d=\"M248,88a8,8,0,0,0-8,8v64a8,8,0,0,0,16,0V96A8,8,0,0,0,248,88Z\" />\n    <path d=\"M200,48H48A24.1,24.1,0,0,0,24,72V184a24.1,24.1,0,0,0,24,24H200a24.1,24.1,0,0,0,24-24V72A24.1,24.1,0,0,0,200,48Zm8,136a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V72a8,8,0,0,1,8-8H200a8,8,0,0,1,8,8Z\" />\n    <rect x=\"56\" y=\"80\" width=\"72\" height=\"96\" rx=\"8\" />\n  </>\n));\n\npathsByWeight.set(\"light\", (color: string) => (\n  <>\n    <line\n      x1=\"248\"\n      y1=\"96\"\n      x2=\"248\"\n      y2=\"160\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <rect\n      x=\"52\"\n      y=\"36\"\n      width=\"144\"\n      height=\"184\"\n      rx=\"16\"\n      transform=\"translate(252 4) rotate(90)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"105.6\"\n      y1=\"168\"\n      x2=\"105.6\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n    <line\n      x1=\"68.8\"\n      y1=\"168\"\n      x2=\"68.8\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"12\"\n    />\n  </>\n));\n\npathsByWeight.set(\"thin\", (color: string) => (\n  <>\n    <line\n      x1=\"248\"\n      y1=\"96\"\n      x2=\"248\"\n      y2=\"160\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <rect\n      x=\"52\"\n      y=\"36\"\n      width=\"144\"\n      height=\"184\"\n      rx=\"16\"\n      transform=\"translate(252 4) rotate(90)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"105.6\"\n      y1=\"168\"\n      x2=\"105.6\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n    <line\n      x1=\"68.8\"\n      y1=\"168\"\n      x2=\"68.8\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"8\"\n    />\n  </>\n));\n\npathsByWeight.set(\"regular\", (color: string) => (\n  <>\n    <line\n      x1=\"248\"\n      y1=\"96\"\n      x2=\"248\"\n      y2=\"160\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <rect\n      x=\"52\"\n      y=\"36\"\n      width=\"144\"\n      height=\"184\"\n      rx=\"16\"\n      transform=\"translate(252 4) rotate(90)\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"105.6\"\n      y1=\"168\"\n      x2=\"105.6\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n    <line\n      x1=\"68.8\"\n      y1=\"168\"\n      x2=\"68.8\"\n      y2=\"88\"\n      fill=\"none\"\n      stroke={color}\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      strokeWidth=\"16\"\n    />\n  </>\n));\n\nconst renderPath: RenderFunction = (weight: IconWeight, color: string) =>\n  renderPathForWeight(weight, color, pathsByWeight);\n\nconst BatteryMedium = forwardRef<SVGSVGElement, IconProps>((props, ref) => (\n  <IconBase ref={ref} {...props} renderPath={renderPath} />\n));\n\nBatteryMedium.displayName = \"BatteryMedium\";\n\nexport default BatteryMedium;\n"], "names": ["pathsByWeight", "Map", "set", "color", "React", "x", "y", "width", "height", "rx", "transform", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "x1", "y1", "x2", "y2", "opacity", "d", "<PERSON><PERSON><PERSON>", "weight", "renderPathForWeight", "BatteryMedium", "forwardRef", "props", "ref", "IconBase", "displayName"], "mappings": ";;;;AAAA;AAWA,IAAMA,aAAa,gBAAG,IAAIC,GAAJ,EAAtB;AAEAD,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAXd,CADF,EAcEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAdF,EAyBEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAzBF,EAoCEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CApCF,CADwB;AAAA,CAA1B;AAmDAf,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVU,IAAAA,OAAO,EAAC;GAPV,CADF,EAUEhB,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAVF,EAqBEX,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAXd,CArBF,EAkCEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAlCF,EA6CEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CA7CF,CAD2B;AAAA,CAA7B;AA4DAf,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B;AAAA,SACxBE,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AAAMiB,IAAAA,CAAC,EAAC;GAAR,CADF,EAEEjB,mBAAA,OAAA;AAAMiB,IAAAA,CAAC,EAAC;GAAR,CAFF,EAGEjB,mBAAA,OAAA;AAAMC,IAAAA,CAAC,EAAC;AAAKC,IAAAA,CAAC,EAAC;AAAKC,IAAAA,KAAK,EAAC;AAAKC,IAAAA,MAAM,EAAC;AAAKC,IAAAA,EAAE,EAAC;GAA9C,CAHF,CADwB;AAAA,CAA1B;AAQAT,aAAa,CAACE,GAAd,CAAkB,OAAlB,EAA2B,UAACC,KAAD;AAAA,SACzBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYEX,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAXd,CAZF,EAyBEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAzBF,EAoCEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CApCF,CADyB;AAAA,CAA3B;AAmDAf,aAAa,CAACE,GAAd,CAAkB,MAAlB,EAA0B,UAACC,KAAD;AAAA,SACxBC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYEX,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAXd,CAZF,EAyBEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAzBF,EAoCEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CApCF,CADwB;AAAA,CAA1B;AAmDAf,aAAa,CAACE,GAAd,CAAkB,SAAlB,EAA6B,UAACC,KAAD;AAAA,SAC3BC,mBAAA,eAAA,MAAA,EACEA,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CADF,EAYEX,mBAAA,OAAA;AACEC,IAAAA,CAAC,EAAC;AACFC,IAAAA,CAAC,EAAC;AACFC,IAAAA,KAAK,EAAC;AACNC,IAAAA,MAAM,EAAC;AACPC,IAAAA,EAAE,EAAC;AACHC,IAAAA,SAAS,EAAC;AACVC,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GAXd,CAZF,EAyBEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CAzBF,EAoCEX,mBAAA,OAAA;AACEY,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHC,IAAAA,EAAE,EAAC;AACHR,IAAAA,IAAI,EAAC;AACLC,IAAAA,MAAM,EAAET;AACRU,IAAAA,aAAa,EAAC;AACdC,IAAAA,cAAc,EAAC;AACfC,IAAAA,WAAW,EAAC;GATd,CApCF,CAD2B;AAAA,CAA7B;;AAmDA,IAAMO,UAAU,GAAmB,SAA7BA,UAA6B,CAACC,MAAD,EAAqBpB,KAArB;AAAA,SACjCqB,mBAAmB,CAACD,MAAD,EAASpB,KAAT,EAAgBH,aAAhB,CADc;AAAA,CAAnC;;AAGA,IAAMyB,aAAa,gBAAGC,UAAU,CAA2B,UAACC,KAAD,EAAQC,GAAR;AAAA,SACzDxB,mBAAA,CAACyB,QAAD;AAAUD,IAAAA,GAAG,EAAEA;KAASD;AAAOL,IAAAA,UAAU,EAAEA;IAA3C,CADyD;AAAA,CAA3B,CAAhC;AAIAG,aAAa,CAACK,WAAd,GAA4B,eAA5B;;;;"}