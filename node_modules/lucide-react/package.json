{"name": "lucide-react", "description": "A Lucide icon library package for React applications", "version": "0.487.0", "license": "ISC", "homepage": "https://lucide.dev", "bugs": "https://github.com/lucide-icons/lucide/issues", "repository": {"type": "git", "url": "https://github.com/lucide-icons/lucide.git", "directory": "packages/lucide-react"}, "keywords": ["Lucide", "React", "<PERSON><PERSON>", "Icons", "Icon", "SVG", "Feather Icons", "Fontawesome", "Font Awesome"], "author": "<PERSON>", "amdName": "lucide-react", "main": "dist/cjs/lucide-react.js", "main:umd": "dist/umd/lucide-react.js", "module": "dist/esm/lucide-react.js", "unpkg": "dist/umd/lucide-react.min.js", "typings": "dist/lucide-react.d.ts", "sideEffects": false, "files": ["dist", "dynamic.mjs", "dynamic.js.map", "dynamic.d.ts", "dynamicIconImports.mjs", "dynamicIconImports.js.map", "dynamicIconImports.d.ts"], "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@types/react": "^18.2.37", "@vitejs/plugin-react": "^4.3.4", "jest-serializer-html": "^7.1.0", "react": "18.2.0", "react-dom": "18.2.0", "rollup": "^4.22.4", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-preserve-directives": "^0.4.0", "typescript": "^4.9.5", "vite": "5.4.14", "vitest": "^1.1.1", "@lucide/build-icons": "1.1.0", "@lucide/shared": "1.0.0", "@lucide/rollup-plugins": "1.0.0"}, "peerDependencies": {"react": "^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "scripts": {"build": "pnpm clean && pnpm copy:license && pnpm build:icons && pnpm typecheck && pnpm build:bundles", "copy:license": "cp ../../LICENSE ./LICENSE", "clean": "rm -rf dist && rm -rf stats && rm -rf ./src/icons/*.ts && rm -f dynamic.* && rm -f dynamicIconImports.d.ts", "build:icons": "build-icons --output=./src --templateSrc=./scripts/exportTemplate.mjs --renderUniqueKey --withAliases --withDynamicImports --separateAliasesFile --aliasesFileExtension=.ts --iconFileExtension=.ts --exportFileName=index.ts", "build:bundles": "rollup -c ./rollup.config.mjs", "typecheck": "tsc", "typecheck:watch": "tsc -w", "test": "pnpm build:icons && vitest run", "test:watch": "vitest watch", "version": "pnpm version --git-tag-version=false"}}